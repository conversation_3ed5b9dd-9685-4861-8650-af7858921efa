import { useState } from 'react';
import DialogSystem from './components/DialogSystem';
import RandomEvents from './components/RandomEvents';
import AchievementSystem from './components/AchievementSystem';

// 搞笑台词库
const funnyQuotes = [
  "教练：我们今天的战术是什么？球员：尽量不要输得太惨...",
  "球迷：为什么我们总是输球？经理：因为对手总是想赢球啊！",
  "记者：请问您对球队有什么期待？教练：期待他们能找到球门在哪里...",
  "啦啦队：常州队加油！球员：你们确定不是在讽刺我们吗？",
  "门将：我今天状态很好！教练：那你能接住几个球？门将：...这个...",
  "前锋：我要进球！后卫：兄弟，先学会不要进自己家球门行吗？",
  "球迷A：我们什么时候能夺冠？球迷B：等恐龙复活的时候吧...",
  "经理：我们的预算很紧张。球员：那我们能不能少输几个球来省点医药费？",
  "记者：常州队的特色是什么？教练：我们很擅长让对手有成就感...",
  "球员：教练，我想转会。教练：去哪？球员：恐龙园当恐龙，至少不用跑步...",
  "医生：你们队伤病率很高啊。队医：主要是笑岔气的比较多...",
  "赞助商：我们为什么要赞助你们？经理：因为我们便宜啊！"
];

const getRandomQuote = () => funnyQuotes[Math.floor(Math.random() * funnyQuotes.length)];

function App() {
  const [currentView, setCurrentView] = useState('dashboard');
  const [currentQuote, setCurrentQuote] = useState(getRandomQuote());
  const [showDialog, setShowDialog] = useState(false);
  // 游戏核心状态 - 基于13轮苏超赛程
  const [gameStats, setGameStats] = useState({
    budget: 1750,
    reputation: 70,
    fans: 15,
    winRate: 23,
    mood: '复杂',
    currentRound: 1, // 当前轮次 (1-13)
    totalWins: 0,
    totalDraws: 0,
    totalLosses: 0,
    totalGoals: 0,
    totalConceded: 0,
    seasonFinished: false
  });

  // 苏超13轮完整赛程
  const suChaoSchedule = [
    { round: 1, date: '5月18日', opponent: '淮安队', venue: '客场', time: '15:00' },
    { round: 2, date: '5月25日', opponent: '徐州队', venue: '主场', time: '19:30' },
    { round: 3, date: '6月1日', opponent: '镇江队', venue: '客场', time: '15:00' },
    { round: 4, date: '6月15日', opponent: '无锡队', venue: '客场', time: '19:30' },
    { round: 5, date: '6月22日', opponent: '苏州队', venue: '主场', time: '19:30' },
    { round: 6, date: '6月29日', opponent: '南通队', venue: '客场', time: '15:00' },
    { round: 7, date: '7月6日', opponent: '扬州队', venue: '主场', time: '19:30' },
    { round: 8, date: '7月13日', opponent: '泰州队', venue: '客场', time: '15:00' },
    { round: 9, date: '7月20日', opponent: '盐城队', venue: '主场', time: '19:30' },
    { round: 10, date: '8月3日', opponent: '连云港队', venue: '客场', time: '15:00' },
    { round: 11, date: '9月13日', opponent: '宿迁队', venue: '主场', time: '19:00' },
    { round: 12, date: '9月21日', opponent: '南京队', venue: '客场', time: '19:30' },
    { round: 13, date: '9月28日', opponent: '宿迁队', venue: '客场', time: '15:00' }
  ];

  // 比赛结果存储 (模拟已完成的比赛)
  const [matchResults, setMatchResults] = useState([
    { round: 1, score: '0-4', result: 'loss', goals: 0, conceded: 4, opponent: '淮安队' },
    { round: 2, score: '1-2', result: 'loss', goals: 1, conceded: 2, opponent: '徐州队' },
    { round: 3, score: '0-1', result: 'loss', goals: 0, conceded: 1, opponent: '镇江队' },
    { round: 4, score: '1-3', result: 'loss', goals: 1, conceded: 3, opponent: '无锡队' }
  ]);

  const refreshQuote = () => {
    setCurrentQuote(getRandomQuote());
  };

  const handleDialogEffect = (effect: string) => {
    console.log('对话效果:', effect);
  };

  const handleRandomEvent = (event: any) => {
    setGameStats(prev => ({
      ...prev,
      budget: prev.budget + (event.effects.budget || 0),
      reputation: prev.reputation + (event.effects.reputation || 0),
      fans: prev.fans + (event.effects.fans || 0),
      mood: event.effects.mood || prev.mood
    }));
  };

  const handleAchievementUnlock = (achievement: any) => {
    console.log('成就解锁:', achievement.title);
  };

  const triggerRandomDialog = () => {
    setShowDialog(true);
  };

  const triggerTestAchievement = () => {
    setGameStats(prev => ({
      ...prev,
      budget: 50
    }));
  };

  // 计算当前赛季统计
  const calculateSeasonStats = () => {
    const wins = matchResults.filter(match => match.result === 'win').length;
    const draws = matchResults.filter(match => match.result === 'draw').length;
    const losses = matchResults.filter(match => match.result === 'loss').length;
    const totalGoals = matchResults.reduce((sum, match) => sum + match.goals, 0);
    const totalConceded = matchResults.reduce((sum, match) => sum + match.conceded, 0);
    const totalMatches = matchResults.length;
    const winRate = totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0;

    return { wins, draws, losses, totalGoals, totalConceded, winRate, totalMatches };
  };

  // 模拟下一场比赛
  const simulateNextMatch = () => {
    const currentRound = gameStats.currentRound;
    if (currentRound > 13) {
      alert('🏆 赛季已结束！请查看最终颁奖典礼！');
      return;
    }

    const nextMatch = suChaoSchedule.find(match => match.round === currentRound);
    if (!nextMatch) return;

    // 随机生成比赛结果 (常州队比较菜，胜率较低)
    const outcomes = [
      { score: '0-3', result: 'loss', goals: 0, conceded: 3 },
      { score: '1-2', result: 'loss', goals: 1, conceded: 2 },
      { score: '0-1', result: 'loss', goals: 0, conceded: 1 },
      { score: '0-2', result: 'loss', goals: 0, conceded: 2 },
      { score: '1-3', result: 'loss', goals: 1, conceded: 3 },
      { score: '1-1', result: 'draw', goals: 1, conceded: 1 },
      { score: '0-0', result: 'draw', goals: 0, conceded: 0 },
      { score: '2-1', result: 'win', goals: 2, conceded: 1 },
      { score: '1-0', result: 'win', goals: 1, conceded: 0 }
    ];

    // 权重：输球70%，平局20%，赢球10%
    const weightedOutcomes = [
      ...outcomes.slice(0, 5), ...outcomes.slice(0, 5), ...outcomes.slice(0, 5), // 输球 * 3
      ...outcomes.slice(5, 7), // 平局 * 1
      ...outcomes.slice(7, 9) // 赢球 * 1
    ];

    const randomOutcome = weightedOutcomes[Math.floor(Math.random() * weightedOutcomes.length)];

    // 添加比赛结果
    const newResult = {
      round: currentRound,
      score: randomOutcome.score,
      result: randomOutcome.result,
      goals: randomOutcome.goals,
      conceded: randomOutcome.conceded,
      opponent: nextMatch.opponent
    };

    setMatchResults(prev => [...prev, newResult]);

    // 更新游戏状态
    const newStats = calculateSeasonStats();
    setGameStats(prev => ({
      ...prev,
      currentRound: currentRound + 1,
      totalWins: newStats.wins,
      totalDraws: newStats.draws,
      totalLosses: newStats.losses,
      totalGoals: newStats.totalGoals,
      totalConceded: newStats.totalConceded,
      winRate: newStats.winRate,
      seasonFinished: currentRound >= 13,
      // 根据比赛结果调整其他属性
      budget: prev.budget + (randomOutcome.result === 'win' ? 100 : randomOutcome.result === 'draw' ? 50 : -20),
      reputation: Math.max(0, Math.min(100, prev.reputation + (randomOutcome.result === 'win' ? 5 : randomOutcome.result === 'draw' ? 2 : -3))),
      fans: Math.max(0, prev.fans + (randomOutcome.result === 'win' ? 2 : randomOutcome.result === 'draw' ? 1 : -0.5)),
      mood: randomOutcome.result === 'win' ? '狂欢' : randomOutcome.result === 'draw' ? '还行' : '绝望'
    }));

    // 显示比赛结果
    const resultEmoji = randomOutcome.result === 'win' ? '🎉' : randomOutcome.result === 'draw' ? '😐' : '😭';
    const resultText = randomOutcome.result === 'win' ? '胜利！' : randomOutcome.result === 'draw' ? '平局' : '失败...';

    alert(`${resultEmoji} 第${currentRound}轮比赛结果：\n\n常州队 ${randomOutcome.score} ${nextMatch.opponent}\n\n${resultText}\n\n${getMatchComment(randomOutcome.result, nextMatch.opponent)}`);

    // 检查是否赛季结束
    if (currentRound >= 13) {
      setTimeout(() => {
        showSeasonSummary();
      }, 1000);
    }
  };

  // 获取比赛评论
  const getMatchComment = (result, opponent) => {
    const comments = {
      win: [
        `奇迹！球员们都不敢相信自己的眼睛！`,
        `${opponent}可能是让球了，不然怎么可能输给我们？`,
        `啦啦队比球员还激动，差点冲进场内庆祝！`,
        `球迷们激动得像夺冠一样，有人甚至哭了！`
      ],
      draw: [
        `平局已经是很好的结果了，球员们都很满意！`,
        `门将今天发挥神勇，扑出了好几个必进球！`,
        `虽然没赢，但至少没有惨败，这就是进步！`,
        `球迷们表示：能不输就是胜利！`
      ],
      loss: [
        `又是熟悉的比分...门将表示已经习惯了`,
        `教练在场边无奈摇头，开始怀疑人生`,
        `球员们安慰彼此：至少我们努力了`,
        `球迷们已经开始讨论下赛季的引援计划`
      ]
    };

    const resultComments = comments[result];
    return resultComments[Math.floor(Math.random() * resultComments.length)];
  };

  // 显示赛季总结
  const showSeasonSummary = () => {
    const stats = calculateSeasonStats();
    const finalPosition = getFinalPosition(stats);
    const awards = getSeasonAwards(stats);

    alert(`🏆 2025苏超赛季总结 🏆\n\n📊 最终战绩：\n胜：${stats.wins}场\n平：${stats.draws}场\n负：${stats.losses}场\n进球：${stats.totalGoals}个\n失球：${stats.totalConceded}个\n胜率：${stats.winRate}%\n\n🏅 最终排名：${finalPosition}\n\n🎖️ 获得奖项：\n${awards.join('\n')}\n\n感谢您的陪伴！常州队永远不会放弃！`);
  };

  // 计算最终排名
  const getFinalPosition = (stats) => {
    if (stats.wins >= 8) return '第1名 - 苏超冠军！';
    if (stats.wins >= 6) return '第2-3名 - 亚军争夺者';
    if (stats.wins >= 4) return '第4-6名 - 中游球队';
    if (stats.wins >= 2) return '第7-10名 - 保级成功';
    return '第11-13名 - 降级区';
  };

  // 获取赛季奖项
  const getSeasonAwards = (stats) => {
    const awards = [];

    if (stats.wins >= 8) {
      awards.push('🏆 苏超冠军');
      awards.push('🎖️ 最佳黑马奖');
    } else if (stats.wins >= 6) {
      awards.push('🥈 苏超亚军');
      awards.push('🎖️ 最大进步奖');
    } else if (stats.wins >= 4) {
      awards.push('🎖️ 中游稳定奖');
    } else if (stats.wins >= 2) {
      awards.push('🎖️ 保级英雄奖');
    } else {
      awards.push('🎖️ 最佳努力奖');
      awards.push('🎖️ 最受欢迎球队奖（因为搞笑）');
    }

    if (stats.totalGoals >= 20) {
      awards.push('⚽ 最佳进攻奖');
    } else if (stats.totalGoals <= 5) {
      awards.push('🛡️ 最需要进攻指导奖');
    }

    if (stats.totalConceded <= 15) {
      awards.push('🥅 最佳防守奖');
    } else if (stats.totalConceded >= 35) {
      awards.push('😅 最需要防守训练奖');
    }

    // 特殊奖项
    awards.push('🎭 最佳娱乐价值奖');
    awards.push('💃 最佳啦啦队配合奖');
    awards.push('🏛️ 最佳文旅推广奖');

    return awards;
  };

  const renderDashboard = () => (
    <div>
      {/* 搞笑标题区域 */}
      <div style={{ textAlign: 'center', padding: '2rem 0' }}>
        <h1 style={{
          fontSize: '3rem',
          fontWeight: 'bold',
          background: 'linear-gradient(45deg, #2563eb, #7c3aed)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          marginBottom: '1rem'
        }}>
          假如我是常州 🤔
        </h1>
        <p style={{ fontSize: '1.25rem', color: '#6b7280', marginBottom: '0.5rem' }}>
          足球管理 × 文旅推广 × 城市营销 × 搞笑日常
        </p>
        <p style={{ color: '#9ca3af', fontSize: '1rem' }}>
          带领常州队征战苏超，顺便推广一下龙城文化（如果我们能赢球的话...）
        </p>
      </div>

      {/* 搞笑语录展示 */}
      <div style={{
        background: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        margin: '2rem auto',
        maxWidth: '600px',
        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
        border: '2px dashed #fbbf24'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
          <span style={{ fontSize: '1.5rem' }}>💬</span>
          <h3 style={{ fontSize: '1.1rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
            今日搞笑语录
          </h3>
        </div>
        <p style={{
          fontSize: '1rem',
          color: '#374151',
          fontStyle: 'italic',
          lineHeight: '1.6',
          margin: '0 0 1rem 0'
        }}>
          "{currentQuote}"
        </p>
        <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button
            onClick={refreshQuote}
            style={{
              background: '#fbbf24',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              cursor: 'pointer',
              fontWeight: '500'
            }}
          >
            🎲 换一句
          </button>
          
          <button
            onClick={triggerRandomDialog}
            style={{
              background: '#8b5cf6',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              cursor: 'pointer',
              fontWeight: '500'
            }}
          >
            💬 随机对话
          </button>

          <button
            onClick={triggerTestAchievement}
            style={{
              background: '#ef4444',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              cursor: 'pointer',
              fontWeight: '500'
            }}
          >
            🏆 测试成就
          </button>
        </div>
      </div>

      {/* 赛季结束颁奖典礼 */}
      {gameStats.seasonFinished && (
        <div style={{
          background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',
          borderRadius: '16px',
          padding: '2rem',
          margin: '2rem auto',
          maxWidth: '800px',
          boxShadow: '0 8px 16px rgba(0,0,0,0.2)',
          border: '3px solid #d97706',
          textAlign: 'center'
        }}>
          <h2 style={{ fontSize: '2rem', fontWeight: 'bold', color: 'white', marginBottom: '1rem', textShadow: '2px 2px 4px rgba(0,0,0,0.3)' }}>
            🏆 2025苏超赛季颁奖典礼 🏆
          </h2>

          <div style={{ background: 'rgba(255,255,255,0.9)', borderRadius: '12px', padding: '1.5rem', marginBottom: '1rem' }}>
            <h3 style={{ fontSize: '1.5rem', color: '#92400e', marginBottom: '1rem' }}>
              📊 常州队最终战绩
            </h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>
              <div>
                <div style={{ fontSize: '2rem', color: '#22c55e', fontWeight: 'bold' }}>{gameStats.totalWins}</div>
                <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>胜利</div>
              </div>
              <div>
                <div style={{ fontSize: '2rem', color: '#f59e0b', fontWeight: 'bold' }}>{gameStats.totalDraws}</div>
                <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>平局</div>
              </div>
              <div>
                <div style={{ fontSize: '2rem', color: '#ef4444', fontWeight: 'bold' }}>{gameStats.totalLosses}</div>
                <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>失败</div>
              </div>
              <div>
                <div style={{ fontSize: '2rem', color: '#8b5cf6', fontWeight: 'bold' }}>{gameStats.totalGoals}</div>
                <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>进球</div>
              </div>
              <div>
                <div style={{ fontSize: '2rem', color: '#f97316', fontWeight: 'bold' }}>{gameStats.totalConceded}</div>
                <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>失球</div>
              </div>
            </div>

            <div style={{ fontSize: '1.25rem', color: '#1f2937', fontWeight: '600', marginTop: '1rem' }}>
              🏅 最终排名：{getFinalPosition(calculateSeasonStats())}
            </div>
          </div>

          <div style={{ background: 'rgba(255,255,255,0.9)', borderRadius: '12px', padding: '1.5rem' }}>
            <h3 style={{ fontSize: '1.5rem', color: '#92400e', marginBottom: '1rem' }}>
              🎖️ 获得奖项
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              {getSeasonAwards(calculateSeasonStats()).map((award, index) => (
                <div key={index} style={{
                  background: '#fef3c7',
                  borderRadius: '8px',
                  padding: '0.75rem',
                  fontSize: '1rem',
                  color: '#92400e',
                  fontWeight: '500'
                }}>
                  {award}
                </div>
              ))}
            </div>

            <div style={{ marginTop: '1.5rem', fontSize: '1.125rem', color: '#1f2937', fontWeight: '600' }}>
              🎉 感谢您的陪伴！常州队永远不会放弃！ 🎉
            </div>
          </div>
        </div>
      )}

      {/* 游戏状态面板 */}
      <div style={{
        marginTop: '3rem',
        background: 'linear-gradient(45deg, #fef3c7, #fde68a)',
        borderRadius: '16px',
        padding: '1.5rem',
        border: '2px solid #f59e0b'
      }}>
        <h3 style={{
          fontSize: '1.25rem',
          fontWeight: 'bold',
          textAlign: 'center',
          marginBottom: '1rem',
          color: '#92400e'
        }}>
          🎮 游戏状态面板 🎮
        </h3>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
          gap: '1rem',
          textAlign: 'center'
        }}>
          <div>
            <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>💰</div>
            <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>预算</div>
            <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>{gameStats.budget}万</div>
            <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>（还够买几包辣条）</div>
          </div>

          <div>
            <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>⭐</div>
            <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>声望</div>
            <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>{gameStats.reputation}</div>
            <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>（勉强及格水平）</div>
          </div>

          <div>
            <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>👥</div>
            <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>粉丝</div>
            <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>{gameStats.fans}万</div>
            <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>（大部分是亲戚朋友）</div>
          </div>

          <div>
            <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>🏆</div>
            <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>胜率</div>
            <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>{gameStats.winRate}%</div>
            <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
              {gameStats.winRate === 0 ? '（还没开始...）' : gameStats.winRate < 20 ? '（惨不忍睹）' : gameStats.winRate < 50 ? '（勉强及格）' : '（超出预期！）'}
            </div>
          </div>

          <div>
            <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>
              {gameStats.mood === '狂欢' ? '🎉' : gameStats.mood === '还行' ? '😐' : gameStats.mood === '绝望' ? '😭' : '🤔'}
            </div>
            <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>球迷心情</div>
            <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>{gameStats.mood}</div>
            <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
              {gameStats.seasonFinished ? '（赛季结束了）' : `（第${gameStats.currentRound > 13 ? 13 : gameStats.currentRound}轮）`}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTeamManagement = () => (
    <div style={{ padding: '2rem 0' }}>
      <h1 style={{ fontSize: '2.5rem', textAlign: 'center', marginBottom: '2rem', color: '#1f2937' }}>
        ⚽ 球队管理 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（试图管理中...）</span>
      </h1>
      
      <div style={{
        background: 'white',
        borderRadius: '16px',
        padding: '2rem',
        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
        marginBottom: '2rem'
      }}>
        <h2 style={{ fontSize: '1.5rem', marginBottom: '1.5rem', color: '#1f2937' }}>
          🏃‍♂️ 当前阵容 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（能上场的都在这了）</span>
        </h2>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem'
        }}>
          {[
            { name: '张三', position: '门将', skill: 65, mood: '紧张', note: '经常被自己绊倒' },
            { name: '李四', position: '后卫', skill: 58, mood: '困惑', note: '分不清哪边是自己球门' },
            { name: '王五', position: '中场', skill: 72, mood: '乐观', note: '传球准确率...算了不说了' },
            { name: '赵六', position: '前锋', skill: 69, mood: '饥饿', note: '进球欲望很强，就是踢不进' }
          ].map((player, index) => (
            <div key={index} style={{
              background: '#f9fafb',
              borderRadius: '12px',
              padding: '1rem',
              border: '2px solid #e5e7eb'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1f2937', margin: 0 }}>
                  {player.name}
                </h3>
                <span style={{
                  background: player.skill > 65 ? '#dcfce7' : player.skill > 55 ? '#fef3c7' : '#fee2e2',
                  color: player.skill > 65 ? '#166534' : player.skill > 55 ? '#92400e' : '#991b1b',
                  padding: '0.25rem 0.5rem',
                  borderRadius: '6px',
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}>
                  {player.skill}分
                </span>
              </div>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0.25rem 0' }}>
                位置: {player.position}
              </p>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0.25rem 0' }}>
                心情: {player.mood}
              </p>
              <p style={{ fontSize: '0.75rem', color: '#f59e0b', fontStyle: 'italic', margin: '0.5rem 0 0 0' }}>
                💡 {player.note}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%)',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* 头部导航 */}
      <header style={{
        background: 'white',
        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
        borderBottom: '2px solid #e5e7eb',
        padding: '1rem 0'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div style={{
                width: '48px',
                height: '48px',
                borderRadius: '50%',
                background: '#2563eb',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '18px'
              }}>
                常
              </div>
              <div>
                <h1 style={{ fontSize: '20px', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                  常州队 🦕
                </h1>
                <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>
                  常州 | 奥体中心 | 专业陪跑30年
                </p>
              </div>
            </div>

            <nav style={{ display: 'flex', gap: '1rem' }}>
              {[
                { id: 'dashboard', label: '总览', emoji: '📊' },
                { id: 'team', label: '球队管理', emoji: '⚽' },
                { id: 'match', label: '比赛中心', emoji: '🏆' },
                { id: 'cheerleaders', label: '啦啦队', emoji: '💃' },
                { id: 'tourism', label: '文旅推广', emoji: '🏛️' }
              ].map(item => (
                <button
                  key={item.id}
                  onClick={() => setCurrentView(item.id)}
                  style={{
                    padding: '8px 16px',
                    borderRadius: '8px',
                    border: 'none',
                    background: currentView === item.id ? '#dbeafe' : 'transparent',
                    color: currentView === item.id ? '#1d4ed8' : '#6b7280',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500',
                    transition: 'all 0.2s'
                  }}
                >
                  {item.emoji} {item.label}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </header>

      <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '1.5rem' }}>
        {currentView === 'dashboard' && renderDashboard()}
        {currentView === 'team' && renderTeamManagement()}
        {currentView === 'match' && (
          <div style={{ padding: '2rem 0' }}>
            <h1 style={{ fontSize: '2.5rem', textAlign: 'center', marginBottom: '2rem', color: '#1f2937' }}>
              🏆 比赛中心 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（希望能赢一场）</span>
            </h1>

            {/* 苏超13轮赛程 */}
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '2rem',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
              marginBottom: '2rem'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
                <h2 style={{ fontSize: '1.5rem', color: '#1f2937', margin: 0 }}>
                  📅 2025苏超联赛赛程 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（13轮征程）</span>
                </h2>
                <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
                  <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                    当前第{gameStats.currentRound > 13 ? 13 : gameStats.currentRound}轮
                  </span>
                  {gameStats.currentRound <= 13 && (
                    <button
                      onClick={simulateNextMatch}
                      style={{
                        background: '#22c55e',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem 1rem',
                        fontSize: '0.875rem',
                        cursor: 'pointer',
                        fontWeight: '600'
                      }}
                    >
                      ⚽ 模拟下一场
                    </button>
                  )}
                </div>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                {suChaoSchedule.map((match, index) => {
                  const result = matchResults.find(r => r.round === match.round);
                  const isPlayed = result !== undefined;
                  const isCurrent = match.round === gameStats.currentRound && !gameStats.seasonFinished;
                  const isUpcoming = match.round > gameStats.currentRound;

                  return (
                    <div key={index} style={{
                      background: isPlayed ? '#fee2e2' : isCurrent ? '#fef3c7' : '#e0f2fe',
                      borderRadius: '12px',
                      padding: '1rem',
                      border: `2px solid ${isPlayed ? '#fca5a5' : isCurrent ? '#fbbf24' : '#7dd3fc'}`,
                      opacity: isUpcoming ? 0.7 : 1
                    }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: '1rem' }}>
                        <div style={{ flex: 1 }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '0.5rem' }}>
                          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1f2937', margin: 0 }}>
                            第{match.round}轮 {match.date} vs {match.opponent}
                          </h3>
                          <span style={{
                            background: isPlayed ? '#dc2626' : isCurrent ? '#f59e0b' : '#0ea5e9',
                            color: 'white',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '6px',
                            fontSize: '0.75rem',
                            fontWeight: '500'
                          }}>
                            {isPlayed ? '已结束' : isCurrent ? '下一场' : '未开始'}
                          </span>
                        </div>
                        <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0.25rem 0' }}>
                          {match.venue} | {match.time} 开球
                        </p>
                        {isPlayed && result && (
                          <p style={{ fontSize: '0.75rem', color: '#f59e0b', fontStyle: 'italic', margin: '0.25rem 0 0 0' }}>
                            💡 {getMatchComment(result.result, result.opponent)}
                          </p>
                        )}
                        {isCurrent && (
                          <p style={{ fontSize: '0.75rem', color: '#f59e0b', fontStyle: 'italic', margin: '0.25rem 0 0 0' }}>
                            🔥 即将开始，点击上方按钮模拟比赛！
                          </p>
                        )}
                      </div>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '2rem', marginBottom: '0.25rem' }}>
                          {isPlayed ? (result.result === 'win' ? '🎉' : result.result === 'draw' ? '😐' : '😭') :
                           isCurrent ? '⚽' : '⏳'}
                        </div>
                        <div style={{
                          fontSize: '1.25rem',
                          fontWeight: 'bold',
                          color: isPlayed && result ?
                            (result.result === 'win' ? '#22c55e' : result.result === 'draw' ? '#f59e0b' : '#dc2626') :
                            '#6b7280'
                        }}>
                          {isPlayed && result ? result.score : 'vs'}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* 本赛季战绩统计 */}
            <div style={{
              background: 'linear-gradient(45deg, #dbeafe, #bfdbfe)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '2px solid #3b82f6',
              marginBottom: '2rem'
            }}>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '1rem', color: '#1e40af' }}>
                📊 本赛季战绩 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（不忍直视）</span>
              </h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '1rem' }}>
                {(() => {
                  const stats = calculateSeasonStats();
                  return [
                    { label: '总场次', value: `${stats.totalMatches}场`, note: stats.totalMatches > 0 ? '每场都是煎熬' : '赛季即将开始', color: '#6b7280' },
                    { label: '胜利', value: `${stats.wins}场`, note: stats.wins > 0 ? '奇迹时刻' : '还没有胜利...', color: '#22c55e' },
                    { label: '平局', value: `${stats.draws}场`, note: stats.draws > 0 ? '运气不错' : '平局都很难', color: '#f59e0b' },
                    { label: '失败', value: `${stats.losses}场`, note: stats.losses > 0 ? '正常发挥' : '希望不要输', color: '#ef4444' },
                    { label: '进球', value: `${stats.totalGoals}个`, note: stats.totalGoals > 0 ? '含乌龙球' : '进球困难户', color: '#8b5cf6' },
                    { label: '失球', value: `${stats.totalConceded}个`, note: stats.totalConceded > 0 ? '门将很努力了' : '防守还行', color: '#f97316' }
                  ];
                })().map((stat, index) => (
                  <div key={index} style={{
                    background: 'rgba(255,255,255,0.8)',
                    borderRadius: '8px',
                    padding: '1rem',
                    textAlign: 'center'
                  }}>
                    <h4 style={{ fontSize: '1.5rem', fontWeight: '600', color: stat.color, margin: '0 0 0.25rem 0' }}>
                      {stat.value}
                    </h4>
                    <p style={{ fontSize: '0.875rem', color: '#374151', margin: '0.25rem 0' }}>
                      {stat.label}
                    </p>
                    <p style={{ fontSize: '0.75rem', color: '#f59e0b', fontStyle: 'italic', margin: '0.25rem 0 0 0' }}>
                      {stat.note}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* 比赛模拟器 */}
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '2rem',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
              marginBottom: '2rem'
            }}>
              <h2 style={{ fontSize: '1.5rem', marginBottom: '1.5rem', color: '#1f2937' }}>
                🎮 比赛模拟器 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（虚拟也能输球）</span>
              </h2>

              <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                <div style={{
                  background: 'linear-gradient(45deg, #22c55e, #16a34a)',
                  borderRadius: '12px',
                  padding: '2rem',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'repeating-linear-gradient(90deg, transparent, transparent 10px, rgba(255,255,255,0.1) 10px, rgba(255,255,255,0.1) 20px)',
                    pointerEvents: 'none'
                  }}></div>

                  <div style={{ position: 'relative', zIndex: 1 }}>
                    <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem', margin: 0 }}>
                      ⚽ 常州队 vs 虚拟对手 ⚽
                    </h3>
                    <div style={{ fontSize: '3rem', fontWeight: 'bold', margin: '1rem 0' }}>
                      0 - 0
                    </div>
                    <p style={{ fontSize: '1rem', opacity: 0.9, margin: 0 }}>
                      点击下方按钮开始比赛模拟
                    </p>
                  </div>
                </div>
              </div>

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
                <button
                  onClick={() => {
                    const outcomes = [
                      { result: '0-3', comment: '又是熟悉的比分...门将表示已经习惯了' },
                      { result: '1-2', comment: '差点就平了！可惜"差点"不能当分数' },
                      { result: '0-1', comment: '小败！这已经是很好的结果了' },
                      { result: '2-1', comment: '奇迹！球员们都不敢相信自己的眼睛' },
                      { result: '1-1', comment: '平局！球迷们激动得像夺冠一样' },
                      { result: '0-4', comment: '惨败...教练开始怀疑人生' }
                    ];
                    const outcome = outcomes[Math.floor(Math.random() * outcomes.length)];
                    alert(`🏆 比赛结果：${outcome.result}\n\n💬 ${outcome.comment}`);
                  }}
                  style={{
                    background: '#22c55e',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '1rem 2rem',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    fontWeight: '600',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => e.target.style.background = '#16a34a'}
                  onMouseOut={(e) => e.target.style.background = '#22c55e'}
                >
                  🎲 开始比赛
                </button>

                <button
                  onClick={() => {
                    const tactics = [
                      '4-4-2 经典阵型（经典输球）',
                      '3-5-2 攻击阵型（攻击对方笑点）',
                      '5-4-1 防守阵型（防守自己的尊严）',
                      '4-3-3 全攻全守（全输全败）',
                      '自由发挥（自由发挥想象力）'
                    ];
                    const tactic = tactics[Math.floor(Math.random() * tactics.length)];
                    alert(`📋 战术安排：${tactic}\n\n💡 球员们表示：战术是什么？能吃吗？`);
                  }}
                  style={{
                    background: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '1rem 2rem',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    fontWeight: '600',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => e.target.style.background = '#2563eb'}
                  onMouseOut={(e) => e.target.style.background = '#3b82f6'}
                >
                  📋 随机战术
                </button>

                <button
                  onClick={() => {
                    const events = [
                      '第15分钟：张三门将扑救失误，球从腋下滚进球门 😅',
                      '第23分钟：李四后卫乌龙球，把球踢进了自己家球门 🤦‍♂️',
                      '第34分钟：王五中场传球失误，直接传给了对方前锋 😰',
                      '第45分钟：赵六前锋射门打中门柱，球弹回来砸到自己脸上 🤕',
                      '第67分钟：裁判出示红牌，原因是球员问他"这是什么颜色的卡？" 🟥',
                      '第78分钟：啦啦队表演太精彩，球员都停下来观看 💃',
                      '第89分钟：教练激动地冲进场内，结果被自己绊倒 🏃‍♂️'
                    ];
                    const event = events[Math.floor(Math.random() * events.length)];
                    alert(`📺 比赛事件：\n\n${event}\n\n解说员：这...这就是常州队的魅力所在！`);
                  }}
                  style={{
                    background: '#f59e0b',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '1rem 2rem',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    fontWeight: '600',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => e.target.style.background = '#d97706'}
                  onMouseOut={(e) => e.target.style.background = '#f59e0b'}
                >
                  📺 随机事件
                </button>
              </div>
            </div>

            {/* 球员状态 */}
            <div style={{
              background: 'linear-gradient(45deg, #fef3c7, #fde68a)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '2px solid #f59e0b'
            }}>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '1rem', color: '#92400e' }}>
                👥 球员比赛状态 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（心理阴影面积）</span>
              </h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
                {[
                  { name: '张三', position: '门将', condition: '紧张', confidence: '30%', note: '害怕球飞过来' },
                  { name: '李四', position: '后卫', condition: '困惑', confidence: '45%', note: '还在找自己的位置' },
                  { name: '王五', position: '中场', condition: '乐观', confidence: '70%', note: '相信奇迹会发生' },
                  { name: '赵六', position: '前锋', condition: '饥饿', confidence: '60%', note: '渴望进球和晚饭' }
                ].map((player, index) => (
                  <div key={index} style={{
                    background: 'rgba(255,255,255,0.8)',
                    borderRadius: '8px',
                    padding: '1rem',
                    textAlign: 'center'
                  }}>
                    <h4 style={{ fontSize: '1rem', fontWeight: '600', color: '#1f2937', margin: '0 0 0.5rem 0' }}>
                      {player.name}
                    </h4>
                    <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0.25rem 0' }}>
                      {player.position} | {player.condition}
                    </p>
                    <div style={{
                      background: '#e5e7eb',
                      borderRadius: '4px',
                      height: '8px',
                      margin: '0.5rem 0',
                      overflow: 'hidden'
                    }}>
                      <div style={{
                        background: parseInt(player.confidence) > 60 ? '#22c55e' : parseInt(player.confidence) > 40 ? '#f59e0b' : '#ef4444',
                        height: '100%',
                        width: player.confidence,
                        transition: 'width 0.3s ease'
                      }}></div>
                    </div>
                    <p style={{ fontSize: '0.75rem', color: '#22c55e', fontWeight: '600', margin: '0.25rem 0' }}>
                      信心值: {player.confidence}
                    </p>
                    <p style={{ fontSize: '0.75rem', color: '#f59e0b', fontStyle: 'italic', margin: '0.25rem 0 0 0' }}>
                      {player.note}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* 球迷评论区 */}
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '2rem',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '1rem', color: '#1f2937' }}>
                💬 球迷评论区 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（真实想法）</span>
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                {[
                  {
                    name: '老球迷张大爷',
                    avatar: '👴',
                    comment: '我看了30年球，从来没见过这么...有创意的踢法',
                    time: '2分钟前',
                    likes: 99
                  },
                  {
                    name: '恐龙园工作人员',
                    avatar: '🦕',
                    comment: '我们恐龙园的恐龙都比他们踢得好，要不要来试试？',
                    time: '5分钟前',
                    likes: 156
                  },
                  {
                    name: '常州本地人',
                    avatar: '🏠',
                    comment: '虽然输球，但是我们的银丝面还是很好吃的！欢迎大家来常州！',
                    time: '8分钟前',
                    likes: 78
                  },
                  {
                    name: '啦啦队粉丝',
                    avatar: '💃',
                    comment: '我是来看啦啦队的，球队输赢无所谓，小姐姐们太棒了！',
                    time: '12分钟前',
                    likes: 234
                  },
                  {
                    name: '对手球迷',
                    avatar: '😏',
                    comment: '谢谢常州队，让我们轻松拿到3分，你们真是好人！',
                    time: '15分钟前',
                    likes: 45
                  },
                  {
                    name: '足球解说员',
                    avatar: '🎙️',
                    comment: '这场比赛让我重新定义了足球...原来还可以这样踢',
                    time: '20分钟前',
                    likes: 123
                  }
                ].map((comment, index) => (
                  <div key={index} style={{
                    background: '#f9fafb',
                    borderRadius: '8px',
                    padding: '1rem',
                    border: '1px solid #e5e7eb'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.5rem' }}>
                      <div style={{ fontSize: '1.5rem' }}>{comment.avatar}</div>
                      <div style={{ flex: 1 }}>
                        <h4 style={{ fontSize: '0.875rem', fontWeight: '600', color: '#1f2937', margin: 0 }}>
                          {comment.name}
                        </h4>
                        <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>
                          {comment.time}
                        </p>
                      </div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                        <span style={{ fontSize: '0.875rem' }}>👍</span>
                        <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>{comment.likes}</span>
                      </div>
                    </div>
                    <p style={{ fontSize: '0.875rem', color: '#374151', margin: 0, lineHeight: '1.4' }}>
                      {comment.comment}
                    </p>
                  </div>
                ))}
              </div>

              <div style={{
                marginTop: '1.5rem',
                padding: '1rem',
                background: '#f3f4f6',
                borderRadius: '8px',
                textAlign: 'center'
              }}>
                <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0 0 1rem 0' }}>
                  💭 想发表评论？
                </p>
                <button
                  onClick={() => {
                    const comments = [
                      '加油常州队！虽然输了，但是精神可嘉！',
                      '我觉得门将已经很努力了，毕竟对手进球太多...',
                      '建议球队去恐龙园学习一下恐龙的生存技巧',
                      '啦啦队表演真的很棒，比比赛精彩多了！',
                      '常州的银丝面真的很好吃，推荐大家来尝尝！',
                      '教练，要不我们换个运动试试？比如踢毽子？'
                    ];
                    const randomComment = comments[Math.floor(Math.random() * comments.length)];
                    alert(`💬 您的评论：\n\n"${randomComment}"\n\n已发布！获得了${Math.floor(Math.random() * 50 + 10)}个赞！`);
                  }}
                  style={{
                    background: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    padding: '0.5rem 1rem',
                    fontSize: '0.875rem',
                    cursor: 'pointer',
                    fontWeight: '500'
                  }}
                >
                  📝 随机评论
                </button>
              </div>
            </div>
          </div>
        )}
        {currentView === 'cheerleaders' && (
          <div style={{ padding: '2rem 0' }}>
            <h1 style={{ fontSize: '2.5rem', textAlign: 'center', marginBottom: '2rem', color: '#1f2937' }}>
              💃 啦啦队管理 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（颜值担当）</span>
            </h1>

            {/* 啦啦队成员 */}
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '2rem',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
              marginBottom: '2rem'
            }}>
              <h2 style={{ fontSize: '1.5rem', marginBottom: '1.5rem', color: '#1f2937' }}>
                ⭐ 啦啦队成员 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（比球员更受欢迎）</span>
              </h2>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
                gap: '1.5rem'
              }}>
                {[
                  {
                    name: '小美',
                    specialty: '舞蹈',
                    popularity: 95,
                    note: '观众都是为她来的',
                    avatar: '👸',
                    experience: '3年',
                    signature: '优雅天鹅舞',
                    fanCount: '2.3万'
                  },
                  {
                    name: '小丽',
                    specialty: '歌唱',
                    popularity: 88,
                    note: '声音比球员踢球好听',
                    avatar: '🎤',
                    experience: '2年',
                    signature: '高音震天',
                    fanCount: '1.8万'
                  },
                  {
                    name: '小芳',
                    specialty: '体操',
                    popularity: 92,
                    note: '比球员动作更标准',
                    avatar: '🤸‍♀️',
                    experience: '4年',
                    signature: '空中翻转',
                    fanCount: '2.1万'
                  },
                  {
                    name: '小红',
                    specialty: '主持',
                    popularity: 85,
                    note: '解说比专业解说员好',
                    avatar: '🎙️',
                    experience: '1年',
                    signature: '妙语连珠',
                    fanCount: '1.5万'
                  },
                  {
                    name: '小青',
                    specialty: '编舞',
                    popularity: 90,
                    note: '设计的动作球员都学不会',
                    avatar: '💫',
                    experience: '5年',
                    signature: '创意无限',
                    fanCount: '1.9万'
                  },
                  {
                    name: '小白',
                    specialty: '道具',
                    popularity: 82,
                    note: '道具比球队装备还专业',
                    avatar: '🎭',
                    experience: '2年',
                    signature: '魔法道具',
                    fanCount: '1.3万'
                  }
                ].map((member, index) => (
                  <div key={index} style={{
                    background: 'linear-gradient(45deg, #fce7f3, #fbcfe8)',
                    borderRadius: '16px',
                    padding: '1.5rem',
                    border: '2px solid #f472b6',
                    position: 'relative',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      position: 'absolute',
                      top: '-10px',
                      right: '-10px',
                      background: '#ec4899',
                      color: 'white',
                      borderRadius: '50%',
                      width: '40px',
                      height: '40px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '0.75rem',
                      fontWeight: 'bold'
                    }}>
                      #{index + 1}
                    </div>

                    <div style={{ textAlign: 'center', marginBottom: '1rem' }}>
                      <div style={{ fontSize: '3rem', marginBottom: '0.5rem' }}>{member.avatar}</div>
                      <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1f2937', margin: '0 0 0.25rem 0' }}>
                        {member.name}
                      </h3>
                      <p style={{ fontSize: '0.875rem', color: '#ec4899', fontWeight: '500', margin: 0 }}>
                        {member.specialty}专家
                      </p>
                    </div>

                    <div style={{ marginBottom: '1rem' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                        <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>人气值</span>
                        <span style={{ fontSize: '0.75rem', color: '#ec4899', fontWeight: '600' }}>{member.popularity}%</span>
                      </div>
                      <div style={{
                        background: '#e5e7eb',
                        borderRadius: '4px',
                        height: '8px',
                        overflow: 'hidden'
                      }}>
                        <div style={{
                          background: 'linear-gradient(45deg, #ec4899, #be185d)',
                          height: '100%',
                          width: `${member.popularity}%`,
                          transition: 'width 0.3s ease'
                        }}></div>
                      </div>
                    </div>

                    <div style={{ fontSize: '0.75rem', color: '#6b7280', marginBottom: '1rem' }}>
                      <p style={{ margin: '0.25rem 0' }}>📅 经验: {member.experience}</p>
                      <p style={{ margin: '0.25rem 0' }}>✨ 招牌: {member.signature}</p>
                      <p style={{ margin: '0.25rem 0' }}>👥 粉丝: {member.fanCount}</p>
                    </div>

                    <p style={{ fontSize: '0.75rem', color: '#f59e0b', fontStyle: 'italic', margin: 0, textAlign: 'center' }}>
                      💡 {member.note}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* 表演节目单 */}
            <div style={{
              background: 'linear-gradient(45deg, #fef3c7, #fde68a)',
              borderRadius: '16px',
              padding: '2rem',
              border: '2px solid #f59e0b',
              marginBottom: '2rem'
            }}>
              <h3 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '1.5rem', color: '#92400e' }}>
                🎭 表演节目单 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（比比赛更精彩）</span>
              </h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
                {[
                  {
                    time: '开场前15分钟',
                    show: '欢迎舞蹈',
                    performer: '全体成员',
                    effect: '观众情绪高涨',
                    duration: '5分钟',
                    music: '《常州欢迎你》'
                  },
                  {
                    time: '中场休息',
                    show: '互动游戏',
                    performer: '小红主持',
                    effect: '比比赛更有趣',
                    duration: '15分钟',
                    music: '《恐龙园之歌》'
                  },
                  {
                    time: '进球时',
                    show: '庆祝表演',
                    performer: '小美领舞',
                    effect: '很少用到...',
                    duration: '2分钟',
                    music: '《胜利进行曲》'
                  },
                  {
                    time: '失球时',
                    show: '安慰舞蹈',
                    performer: '小芳体操',
                    effect: '经常表演',
                    duration: '3分钟',
                    music: '《明天会更好》'
                  },
                  {
                    time: '终场哨响',
                    show: '感谢表演',
                    performer: '小丽独唱',
                    effect: '观众不愿离开',
                    duration: '8分钟',
                    music: '《再见朋友》'
                  },
                  {
                    time: '加时赛',
                    show: '鼓励加油',
                    performer: '小青编舞',
                    effect: '比球员还累',
                    duration: '持续进行',
                    music: '《永不放弃》'
                  }
                ].map((item, index) => (
                  <div key={index} style={{
                    background: 'rgba(255,255,255,0.9)',
                    borderRadius: '12px',
                    padding: '1.5rem',
                    textAlign: 'center',
                    border: '1px solid #f59e0b'
                  }}>
                    <h4 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1f2937', margin: '0 0 0.5rem 0' }}>
                      {item.time}
                    </h4>
                    <div style={{
                      background: '#fbbf24',
                      color: 'white',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      margin: '0.5rem 0',
                      fontWeight: '600'
                    }}>
                      {item.show}
                    </div>
                    <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0' }}>
                      👤 表演者: {item.performer}
                    </p>
                    <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0' }}>
                      ⏱️ 时长: {item.duration}
                    </p>
                    <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0' }}>
                      🎵 配乐: {item.music}
                    </p>
                    <p style={{ fontSize: '0.75rem', color: '#f59e0b', fontStyle: 'italic', margin: '0.5rem 0 0 0' }}>
                      📈 {item.effect}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* 啦啦队训练中心 */}
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '2rem',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
              marginBottom: '2rem'
            }}>
              <h2 style={{ fontSize: '1.5rem', marginBottom: '1.5rem', color: '#1f2937' }}>
                🏋️‍♀️ 训练中心 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（比球员训练认真）</span>
              </h2>

              <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                <div style={{
                  background: 'linear-gradient(45deg, #ec4899, #be185d)',
                  borderRadius: '12px',
                  padding: '2rem',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(255,255,255,0.1) 10px, rgba(255,255,255,0.1) 20px)',
                    pointerEvents: 'none'
                  }}></div>

                  <div style={{ position: 'relative', zIndex: 1 }}>
                    <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem', margin: 0 }}>
                      💃 今日训练项目 💃
                    </h3>
                    <div style={{ fontSize: '2rem', margin: '1rem 0' }}>
                      🎭 表演技巧提升
                    </div>
                    <p style={{ fontSize: '1rem', opacity: 0.9, margin: 0 }}>
                      点击下方按钮开始训练
                    </p>
                  </div>
                </div>
              </div>

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
                <button
                  onClick={() => {
                    const trainings = [
                      { type: '舞蹈训练', result: '小美完成了10个完美转圈，观众席传来掌声！', effect: '+5 魅力值' },
                      { type: '歌唱练习', result: '小丽的高音把玻璃都震碎了，但很好听！', effect: '+3 音乐技能' },
                      { type: '体操训练', result: '小芳的后空翻让教练都看呆了！', effect: '+4 灵活度' },
                      { type: '主持练习', result: '小红的口才让球员们都想转行当解说员！', effect: '+6 表达能力' },
                      { type: '编舞创作', result: '小青设计的新舞蹈连恐龙都想学！', effect: '+7 创意值' },
                      { type: '道具制作', result: '小白做的道具比球队装备还专业！', effect: '+5 制作技能' }
                    ];
                    const training = trainings[Math.floor(Math.random() * trainings.length)];
                    alert(`🏋️‍♀️ 训练结果：\n\n${training.type}\n${training.result}\n\n✨ ${training.effect}`);
                  }}
                  style={{
                    background: '#ec4899',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '1rem 2rem',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    fontWeight: '600',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => e.target.style.background = '#be185d'}
                  onMouseOut={(e) => e.target.style.background = '#ec4899'}
                >
                  💃 开始训练
                </button>

                <button
                  onClick={() => {
                    const performances = [
                      '《常州之光》- 融合恐龙园元素的主题舞蹈',
                      '《银丝面之歌》- 以常州美食为灵感的音乐剧',
                      '《天宁寺祈福》- 传统与现代结合的祈福舞',
                      '《红梅花开》- 春天主题的浪漫芭蕾',
                      '《淹城春秋》- 历史文化主题表演',
                      '《天目湖畔》- 自然风光主题的现代舞'
                    ];
                    const performance = performances[Math.floor(Math.random() * performances.length)];
                    alert(`🎭 新节目创作：\n\n${performance}\n\n观众们表示：这比足球比赛精彩一万倍！`);
                  }}
                  style={{
                    background: '#8b5cf6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '1rem 2rem',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    fontWeight: '600',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => e.target.style.background = '#7c3aed'}
                  onMouseOut={(e) => e.target.style.background = '#8b5cf6'}
                >
                  🎭 创作节目
                </button>

                <button
                  onClick={() => {
                    const events = [
                      '啦啦队受邀参加常州市文艺汇演，获得一等奖！',
                      '恐龙园邀请啦啦队担任形象大使，年薪比球员还高！',
                      '央视记者专程来采访啦啦队，球员们在一旁羡慕地看着',
                      '啦啦队的表演视频在网上爆红，粉丝数突破100万！',
                      '其他球队纷纷挖角我们的啦啦队成员，开出天价合同',
                      '啦啦队成员被邀请参加《舞蹈风暴》，成功晋级总决赛！'
                    ];
                    const event = events[Math.floor(Math.random() * events.length)];
                    alert(`🌟 特殊事件：\n\n${event}\n\n球员们表示：我们什么时候也能这么受欢迎？`);
                  }}
                  style={{
                    background: '#f59e0b',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '1rem 2rem',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    fontWeight: '600',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => e.target.style.background = '#d97706'}
                  onMouseOut={(e) => e.target.style.background = '#f59e0b'}
                >
                  🌟 特殊事件
                </button>
              </div>
            </div>

            {/* 粉丝互动区 */}
            <div style={{
              background: 'linear-gradient(45deg, #e0f2fe, #bae6fd)',
              borderRadius: '16px',
              padding: '2rem',
              border: '2px solid #0ea5e9'
            }}>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '1rem', color: '#0c4a6e' }}>
                💕 粉丝互动区 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（人气爆棚）</span>
              </h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
                {[
                  { metric: '总粉丝数', value: '12.8万', growth: '+2.3万', note: '比球队粉丝多10倍' },
                  { metric: '微博关注', value: '8.5万', growth: '+1.8万', note: '每条微博都是热搜' },
                  { metric: '抖音粉丝', value: '15.2万', growth: '+3.1万', note: '舞蹈视频播放量破千万' },
                  { metric: '现场观众', value: '95%', growth: '+15%', note: '都是来看啦啦队的' },
                  { metric: '媒体报道', value: '156篇', growth: '+89篇', note: '比球队新闻多100倍' },
                  { metric: '商业代言', value: '23个', growth: '+12个', note: '收入超过整个球队' }
                ].map((item, index) => (
                  <div key={index} style={{
                    background: 'rgba(255,255,255,0.9)',
                    borderRadius: '8px',
                    padding: '1rem',
                    textAlign: 'center'
                  }}>
                    <h4 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#0ea5e9', margin: '0 0 0.25rem 0' }}>
                      {item.value}
                    </h4>
                    <p style={{ fontSize: '0.875rem', color: '#374151', margin: '0.25rem 0' }}>
                      {item.metric}
                    </p>
                    <p style={{ fontSize: '0.75rem', color: '#22c55e', fontWeight: '600', margin: '0.25rem 0' }}>
                      {item.growth}
                    </p>
                    <p style={{ fontSize: '0.75rem', color: '#f59e0b', fontStyle: 'italic', margin: '0.25rem 0 0 0' }}>
                      {item.note}
                    </p>
                  </div>
                ))}
              </div>

              <div style={{
                marginTop: '1.5rem',
                padding: '1rem',
                background: 'rgba(255,255,255,0.9)',
                borderRadius: '8px',
                textAlign: 'center'
              }}>
                <p style={{ fontSize: '1rem', color: '#0c4a6e', fontWeight: '600', margin: 0 }}>
                  🎉 啦啦队已成为常州市的文化名片，比足球队更有名！
                </p>
              </div>
            </div>
          </div>
        )}
        {currentView === 'tourism' && (
          <div style={{ padding: '2rem 0' }}>
            <h1 style={{ fontSize: '2.5rem', textAlign: 'center', marginBottom: '2rem', color: '#1f2937' }}>
              🏛️ 文旅推广 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（赚外快中...）</span>
            </h1>

            {/* 推广项目总览 */}
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '2rem',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
              marginBottom: '2rem'
            }}>
              <h2 style={{ fontSize: '1.5rem', marginBottom: '1.5rem', color: '#1f2937' }}>
                🎯 推广项目总览 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（比踢球赚钱）</span>
              </h2>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
                gap: '1.5rem'
              }}>
                {[
                  {
                    project: '恐龙园联名合作',
                    status: '进行中',
                    revenue: '+500万',
                    description: '球员穿恐龙服踢球，门票销量暴增',
                    effect: '观众以为是表演赛，纷纷买票',
                    progress: 85,
                    partner: '中华恐龙园',
                    duration: '全年合作'
                  },
                  {
                    project: '天宁寺祈福活动',
                    status: '已完成',
                    revenue: '+200万',
                    description: '全队去寺庙祈祷能赢球',
                    effect: '和尚都被感动了，免费开光',
                    progress: 100,
                    partner: '天宁寺',
                    duration: '每月一次'
                  },
                  {
                    project: '银丝面代言',
                    status: '谈判中',
                    revenue: '+300万',
                    description: '用银丝面比喻传球技术',
                    effect: '面条都比我们传球顺滑',
                    progress: 60,
                    partner: '常州面馆联盟',
                    duration: '2年合约'
                  },
                  {
                    project: '红梅公园赏花',
                    status: '策划中',
                    revenue: '+150万',
                    description: '春天赏梅，秋天看球',
                    effect: '梅花比我们坚强',
                    progress: 30,
                    partner: '红梅公园',
                    duration: '季节性活动'
                  },
                  {
                    project: '淹城春秋文化',
                    status: '已签约',
                    revenue: '+400万',
                    description: '学习古代兵法用于足球',
                    effect: '古人智慧也救不了我们',
                    progress: 75,
                    partner: '淹城春秋乐园',
                    duration: '3年战略合作'
                  },
                  {
                    project: '天目湖疗愈之旅',
                    status: '热销中',
                    revenue: '+600万',
                    description: '输球后的心灵疗愈套餐',
                    effect: '球迷和球员都需要',
                    progress: 90,
                    partner: '天目湖度假村',
                    duration: '长期合作'
                  }
                ].map((project, index) => (
                  <div key={index} style={{
                    background: project.status === '已完成' ? 'linear-gradient(45deg, #dcfce7, #bbf7d0)' :
                               project.status === '进行中' ? 'linear-gradient(45deg, #fef3c7, #fde68a)' :
                               project.status === '热销中' ? 'linear-gradient(45deg, #dbeafe, #bfdbfe)' :
                               'linear-gradient(45deg, #f3f4f6, #e5e7eb)',
                    borderRadius: '16px',
                    padding: '1.5rem',
                    border: `2px solid ${project.status === '已完成' ? '#22c55e' :
                                        project.status === '进行中' ? '#f59e0b' :
                                        project.status === '热销中' ? '#3b82f6' : '#9ca3af'}`,
                    position: 'relative',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      position: 'absolute',
                      top: '10px',
                      right: '10px',
                      background: project.status === '已完成' ? '#22c55e' :
                                 project.status === '进行中' ? '#f59e0b' :
                                 project.status === '热销中' ? '#3b82f6' : '#9ca3af',
                      color: 'white',
                      borderRadius: '6px',
                      padding: '0.25rem 0.5rem',
                      fontSize: '0.75rem',
                      fontWeight: '600'
                    }}>
                      {project.status}
                    </div>

                    <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1f2937', margin: '0 0 0.5rem 0', paddingRight: '80px' }}>
                      {project.project}
                    </h3>

                    <div style={{ marginBottom: '1rem' }}>
                      <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0.25rem 0' }}>
                        🤝 合作伙伴: {project.partner}
                      </p>
                      <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0.25rem 0' }}>
                        ⏰ 合作期限: {project.duration}
                      </p>
                      <p style={{ fontSize: '0.875rem', color: '#22c55e', fontWeight: '600', margin: '0.25rem 0' }}>
                        💰 预期收入: {project.revenue}
                      </p>
                    </div>

                    <p style={{ fontSize: '0.875rem', color: '#374151', margin: '0.5rem 0', lineHeight: '1.4' }}>
                      📋 {project.description}
                    </p>

                    <div style={{ marginBottom: '1rem' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                        <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>项目进度</span>
                        <span style={{ fontSize: '0.75rem', color: '#1f2937', fontWeight: '600' }}>{project.progress}%</span>
                      </div>
                      <div style={{
                        background: '#e5e7eb',
                        borderRadius: '4px',
                        height: '8px',
                        overflow: 'hidden'
                      }}>
                        <div style={{
                          background: project.status === '已完成' ? '#22c55e' :
                                     project.status === '进行中' ? '#f59e0b' :
                                     project.status === '热销中' ? '#3b82f6' : '#9ca3af',
                          height: '100%',
                          width: `${project.progress}%`,
                          transition: 'width 0.3s ease'
                        }}></div>
                      </div>
                    </div>

                    <p style={{ fontSize: '0.75rem', color: '#f59e0b', fontStyle: 'italic', margin: 0 }}>
                      💡 {project.effect}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* 文旅收入统计 */}
            <div style={{
              background: 'linear-gradient(45deg, #d1fae5, #a7f3d0)',
              borderRadius: '16px',
              padding: '2rem',
              border: '2px solid #22c55e',
              marginBottom: '2rem'
            }}>
              <h3 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '1.5rem', color: '#166534' }}>
                💰 文旅收入统计 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（救命稻草）</span>
              </h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))', gap: '1rem', marginBottom: '2rem' }}>
                {[
                  { category: '景点代言', amount: '1200万', percent: '40%', growth: '+25%', icon: '🏛️' },
                  { category: '美食推广', amount: '800万', percent: '27%', growth: '+18%', icon: '🍜' },
                  { category: '文化活动', amount: '600万', percent: '20%', growth: '+32%', icon: '🎭' },
                  { category: '旅游套餐', amount: '400万', percent: '13%', growth: '+45%', icon: '🎒' }
                ].map((item, index) => (
                  <div key={index} style={{
                    background: 'rgba(255,255,255,0.9)',
                    borderRadius: '12px',
                    padding: '1.5rem',
                    textAlign: 'center',
                    border: '1px solid #22c55e'
                  }}>
                    <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>{item.icon}</div>
                    <h4 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#166534', margin: '0 0 0.25rem 0' }}>
                      {item.amount}
                    </h4>
                    <p style={{ fontSize: '0.875rem', color: '#374151', margin: '0.25rem 0' }}>
                      {item.category}
                    </p>
                    <p style={{ fontSize: '0.75rem', color: '#22c55e', fontWeight: '600', margin: '0.25rem 0' }}>
                      占比 {item.percent}
                    </p>
                    <p style={{ fontSize: '0.75rem', color: '#f59e0b', fontWeight: '600', margin: '0.25rem 0' }}>
                      增长 {item.growth}
                    </p>
                  </div>
                ))}
              </div>

              <div style={{
                padding: '1.5rem',
                background: 'rgba(255,255,255,0.9)',
                borderRadius: '12px',
                textAlign: 'center'
              }}>
                <h4 style={{ fontSize: '1.25rem', color: '#166534', fontWeight: '600', margin: '0 0 0.5rem 0' }}>
                  📊 总收入对比
                </h4>
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: '2rem', flexWrap: 'wrap' }}>
                  <div>
                    <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>文旅收入</p>
                    <p style={{ fontSize: '2rem', color: '#22c55e', fontWeight: 'bold', margin: 0 }}>3000万</p>
                  </div>
                  <div style={{ fontSize: '2rem', color: '#6b7280' }}>VS</div>
                  <div>
                    <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>足球收入</p>
                    <p style={{ fontSize: '2rem', color: '#ef4444', fontWeight: 'bold', margin: 0 }}>1000万</p>
                  </div>
                </div>
                <p style={{ fontSize: '1rem', color: '#166534', fontWeight: '600', margin: '1rem 0 0 0' }}>
                  💡 文旅收入已超过足球收入300%，建议转型做旅游公司！
                </p>
              </div>
            </div>

            {/* 营销活动中心 */}
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '2rem',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
              marginBottom: '2rem'
            }}>
              <h2 style={{ fontSize: '1.5rem', marginBottom: '1.5rem', color: '#1f2937' }}>
                📢 营销活动中心 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（创意无限）</span>
              </h2>

              <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                <div style={{
                  background: 'linear-gradient(45deg, #f59e0b, #d97706)',
                  borderRadius: '12px',
                  padding: '2rem',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(255,255,255,0.1) 10px, rgba(255,255,255,0.1) 20px)',
                    pointerEvents: 'none'
                  }}></div>

                  <div style={{ position: 'relative', zIndex: 1 }}>
                    <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem', margin: 0 }}>
                      🎯 常州文旅推广总部 🎯
                    </h3>
                    <div style={{ fontSize: '2rem', margin: '1rem 0' }}>
                      🏛️ 让世界了解常州
                    </div>
                    <p style={{ fontSize: '1rem', opacity: 0.9, margin: 0 }}>
                      点击下方按钮开始营销活动
                    </p>
                  </div>
                </div>
              </div>

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
                <button
                  onClick={() => {
                    const campaigns = [
                      {
                        name: '恐龙足球节',
                        result: '恐龙园门票销量增长200%，游客都想看恐龙踢球！',
                        revenue: '+800万',
                        effect: '成为全国热点话题'
                      },
                      {
                        name: '银丝面挑战赛',
                        result: '全城面馆生意爆棚，外地游客专程来品尝！',
                        revenue: '+500万',
                        effect: '常州美食走向全国'
                      },
                      {
                        name: '天宁寺祈福游',
                        result: '香火钱暴增，和尚都忙不过来了！',
                        revenue: '+300万',
                        effect: '宗教旅游新热点'
                      },
                      {
                        name: '红梅花海节',
                        result: '春天游客爆满，梅花都被拍照累坏了！',
                        revenue: '+600万',
                        effect: '成为网红打卡地'
                      },
                      {
                        name: '淹城穿越体验',
                        result: '古装租赁生意火爆，大家都想当古人！',
                        revenue: '+700万',
                        effect: '历史文化新体验'
                      },
                      {
                        name: '天目湖疗愈营',
                        result: '心理医生都不够用了，大家排队来疗愈！',
                        revenue: '+900万',
                        effect: 'wellness旅游新标杆'
                      }
                    ];
                    const campaign = campaigns[Math.floor(Math.random() * campaigns.length)];
                    alert(`🎯 营销活动结果：\n\n📢 ${campaign.name}\n\n📈 ${campaign.result}\n\n💰 收入：${campaign.revenue}\n🌟 影响：${campaign.effect}`);
                  }}
                  style={{
                    background: '#f59e0b',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '1rem 2rem',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    fontWeight: '600',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => e.target.style.background = '#d97706'}
                  onMouseOut={(e) => e.target.style.background = '#f59e0b'}
                >
                  🎯 启动营销
                </button>

                <button
                  onClick={() => {
                    const collaborations = [
                      '与上海迪士尼合作，推出"恐龙vs米老鼠"主题活动',
                      '与杭州西湖联动，打造"江南双璧"旅游线路',
                      '与北京故宫合作，举办"古今文化对话"展览',
                      '与成都大熊猫基地联合，推出"恐龙熊猫友谊赛"',
                      '与西安兵马俑合作，开展"春秋战国文化节"',
                      '与桂林山水联手，打造"山水恐龙奇幻之旅"'
                    ];
                    const collaboration = collaborations[Math.floor(Math.random() * collaborations.length)];
                    alert(`🤝 合作项目：\n\n${collaboration}\n\n预计带来游客50万人次，收入增长1000万！\n\n常州正在成为全国文旅新热点！`);
                  }}
                  style={{
                    background: '#8b5cf6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '1rem 2rem',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    fontWeight: '600',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => e.target.style.background = '#7c3aed'}
                  onMouseOut={(e) => e.target.style.background = '#8b5cf6'}
                >
                  🤝 跨城合作
                </button>

                <button
                  onClick={() => {
                    const innovations = [
                      'AR恐龙复活体验：游客可以与虚拟恐龙互动踢球',
                      'VR古城穿越：身临其境体验春秋战国时代',
                      '智能导游机器人：会说常州话的AI导游',
                      '区块链文物认证：每个文物都有数字身份证',
                      '5G全息投影：让古人"复活"讲述历史',
                      '无人机航拍服务：从空中俯瞰常州美景'
                    ];
                    const innovation = innovations[Math.floor(Math.random() * innovations.length)];
                    alert(`🚀 科技创新：\n\n${innovation}\n\n这项黑科技让常州成为全国智慧旅游示范城市！\n\n游客们表示：这比科幻电影还酷！`);
                  }}
                  style={{
                    background: '#06b6d4',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '1rem 2rem',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    fontWeight: '600',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => e.target.style.background = '#0891b2'}
                  onMouseOut={(e) => e.target.style.background = '#06b6d4'}
                >
                  🚀 科技创新
                </button>
              </div>
            </div>

            {/* 游客反馈区 */}
            <div style={{
              background: 'linear-gradient(45deg, #fef3c7, #fde68a)',
              borderRadius: '16px',
              padding: '2rem',
              border: '2px solid #f59e0b'
            }}>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '1rem', color: '#92400e' }}>
                💬 游客反馈区 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（好评如潮）</span>
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                {[
                  {
                    name: '上海游客小王',
                    avatar: '🏙️',
                    comment: '没想到常州这么好玩！恐龙园太震撼了，比迪士尼还刺激！',
                    rating: 5,
                    location: '上海'
                  },
                  {
                    name: '北京大爷老李',
                    avatar: '🏛️',
                    comment: '银丝面真是绝了！我在北京吃了30年面条，这个最香！',
                    rating: 5,
                    location: '北京'
                  },
                  {
                    name: '广州美女小陈',
                    avatar: '🌸',
                    comment: '红梅公园的花海太美了！拍照发朋友圈收获1000个赞！',
                    rating: 5,
                    location: '广州'
                  },
                  {
                    name: '成都吃货小张',
                    avatar: '🐼',
                    comment: '常州的美食太丰富了！我胖了5斤但很开心！',
                    rating: 4,
                    location: '成都'
                  },
                  {
                    name: '杭州文青小刘',
                    avatar: '🍃',
                    comment: '淹城的历史文化底蕴深厚，比我想象的更有内涵！',
                    rating: 5,
                    location: '杭州'
                  },
                  {
                    name: '深圳白领小赵',
                    avatar: '🏢',
                    comment: '天目湖的疗愈之旅太棒了！工作压力瞬间消失！',
                    rating: 5,
                    location: '深圳'
                  }
                ].map((feedback, index) => (
                  <div key={index} style={{
                    background: 'rgba(255,255,255,0.9)',
                    borderRadius: '8px',
                    padding: '1rem',
                    border: '1px solid #f59e0b'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.5rem' }}>
                      <div style={{ fontSize: '1.5rem' }}>{feedback.avatar}</div>
                      <div style={{ flex: 1 }}>
                        <h4 style={{ fontSize: '0.875rem', fontWeight: '600', color: '#1f2937', margin: 0 }}>
                          {feedback.name}
                        </h4>
                        <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>
                          来自 {feedback.location}
                        </p>
                      </div>
                      <div style={{ display: 'flex', gap: '0.125rem' }}>
                        {[...Array(5)].map((_, i) => (
                          <span key={i} style={{
                            color: i < feedback.rating ? '#fbbf24' : '#e5e7eb',
                            fontSize: '0.875rem'
                          }}>
                            ⭐
                          </span>
                        ))}
                      </div>
                    </div>
                    <p style={{ fontSize: '0.875rem', color: '#374151', margin: 0, lineHeight: '1.4' }}>
                      {feedback.comment}
                    </p>
                  </div>
                ))}
              </div>

              <div style={{
                marginTop: '1.5rem',
                padding: '1rem',
                background: 'rgba(255,255,255,0.9)',
                borderRadius: '8px',
                textAlign: 'center'
              }}>
                <p style={{ fontSize: '1rem', color: '#92400e', fontWeight: '600', margin: 0 }}>
                  🎉 常州已成为全国热门旅游目的地，年接待游客突破1000万人次！
                </p>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* 对话系统 */}
      <DialogSystem
        isOpen={showDialog}
        onClose={() => setShowDialog(false)}
        onEffect={handleDialogEffect}
      />

      {/* 随机事件系统 */}
      <RandomEvents onEventTrigger={handleRandomEvent} />

      {/* 成就系统 */}
      <AchievementSystem 
        gameStats={gameStats} 
        onAchievementUnlock={handleAchievementUnlock} 
      />
    </div>
  );
}

export default App;
