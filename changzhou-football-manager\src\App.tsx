import { useEffect, useState } from 'react';
import { useGameStore } from './store/gameStore';
import GameHeader from './components/GameHeader';
import TeamManagement from './components/TeamManagement';
import MatchCenter from './components/MatchCenter';
import CheerleaderManagement from './components/CheerleaderManagement';
import TourismPromotion from './components/TourismPromotion';
import Dashboard from './components/Dashboard';

type GameView = 'dashboard' | 'team' | 'match' | 'cheerleaders' | 'tourism';

function App() {
  const [currentView, setCurrentView] = useState<GameView>('dashboard');
  const initializeGame = useGameStore(state => state.initializeGame);

  useEffect(() => {
    initializeGame();
  }, [initializeGame]);

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard />;
      case 'team':
        return <TeamManagement />;
      case 'match':
        return <MatchCenter />;
      case 'cheerleaders':
        return <CheerleaderManagement />;
      case 'tourism':
        return <TourismPromotion />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <GameHeader currentView={currentView} onViewChange={setCurrentView} />

      <main className="container mx-auto px-4 py-6">
        <div className="animate-fade-in">
          {renderCurrentView()}
        </div>
      </main>

      {/* 游戏标题和介绍 */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-gray-200">
          <h3 className="text-sm font-semibold text-primary-600">假如我是常州</h3>
          <p className="text-xs text-gray-600">足球管理 × 文旅推广</p>
        </div>
      </div>
    </div>
  );
}

export default App;
