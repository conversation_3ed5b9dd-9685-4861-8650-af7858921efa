import { useState } from 'react';
import DialogSystem from './components/DialogSystem';
import RandomEvents from './components/RandomEvents';
import AchievementSystem from './components/AchievementSystem';

// 搞笑台词库
const funnyQuotes = [
  "欢迎来到常州队！我们的目标是：不求最强，但求最搞笑！",
  "教练说：踢球要有创意，就像恐龙园的恐龙一样...呃，恐龙会踢球吗？",
  "球员抱怨：为什么我们的球衣是蓝色的？我还以为是因为经常被打得鼻青脸肿呢！",
  "啦啦队队长：我们的口号是'常州必胜'，但是...能先赢一场再说吗？",
  "球迷：我支持常州队30年了，医生说我可能有受虐倾向...",
  "记者：请问您对这场比赛有什么期待？球员：期待不要输得太惨...",
  "门将：我今天状态很好！教练：那你能不能别让球从你腿间穿过去？",
  "前锋：我要进球！后卫：兄弟，先学会不进乌龙球行吗？",
  "球迷A：我们什么时候能夺冠？球迷B：等恐龙复活的时候吧...",
  "赞助商：我们为什么要赞助你们？经理：因为我们便宜啊！",
  "医生：你们队伤病率很高啊。队医：主要是笑岔气的比较多...",
  "记者：常州队的特色是什么？教练：我们擅长把优势变劣势，把劣势变...更劣势",
  "球员：教练，我想转会。教练：去哪？球员：恐龙园当恐龙，至少不用跑步...",
  "啦啦队：加油！常州队最棒！球员：你们确定在给我们加油吗？",
  "球迷：为什么我们总是输球？经理：因为对手总是比我们强...这很合理吧？",
  "裁判：这是点球！球员：裁判，你确定不是在做梦吗？",
  "教练：我们要学习巴萨的传控。球员：教练，我们连传球都传不准...",
  "记者：常州队今年的目标是什么？经理：活着踢完这个赛季...",
  "球迷：我们的主场优势在哪？球员：观众席的瓜子嗑得比较响？",
  "赞助商：你们的球衣广告位还有吗？经理：有！我们可以在球员脸上贴..."
];

const getRandomQuote = () => funnyQuotes[Math.floor(Math.random() * funnyQuotes.length)];

function App() {
  const [currentView, setCurrentView] = useState('dashboard');
  const [currentQuote, setCurrentQuote] = useState(getRandomQuote());
  const [showDialog, setShowDialog] = useState(false);
  const [gameStats, setGameStats] = useState({
    budget: 1750,
    reputation: 70,
    fans: 15,
    winRate: 23,
    mood: '复杂'
  });

  const refreshQuote = () => {
    setCurrentQuote(getRandomQuote());
  };

  const handleDialogEffect = (effect: string) => {
    // 处理对话效果
    console.log('对话效果:', effect);
    // 这里可以根据效果更新游戏状态
  };

  const handleRandomEvent = (event: any) => {
    // 处理随机事件效果
    setGameStats(prev => ({
      ...prev,
      budget: prev.budget + (event.effects.budget || 0),
      reputation: prev.reputation + (event.effects.reputation || 0),
      fans: prev.fans + (event.effects.fans || 0),
      mood: event.effects.mood || prev.mood
    }));
  };

  const handleAchievementUnlock = (achievement: any) => {
    console.log('成就解锁:', achievement.title);
    // 可以在这里添加额外的奖励逻辑
  };

  const triggerRandomDialog = () => {
    setShowDialog(true);
  };

  const triggerTestAchievement = () => {
    // 测试成就：降低预算触发"破产边缘"成就
    setGameStats(prev => ({
      ...prev,
      budget: 50 // 低于100万触发成就
    }));
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%)',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* 搞笑头部 */}
      <header style={{
        background: 'white',
        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
        borderBottom: '2px solid #e5e7eb',
        padding: '1rem 0'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div style={{
                width: '48px',
                height: '48px',
                borderRadius: '50%',
                background: '#2563eb',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '18px'
              }}>
                常
              </div>
              <div>
                <h1 style={{ fontSize: '20px', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                  常州队 🦕
                </h1>
                <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>
                  常州 | 奥体中心 | 专业陪跑30年
                </p>
              </div>
            </div>

            <nav style={{ display: 'flex', gap: '1rem' }}>
              {[
                { id: 'dashboard', label: '总览', emoji: '📊' },
                { id: 'team', label: '球队管理', emoji: '⚽' },
                { id: 'match', label: '比赛中心', emoji: '🏆' },
                { id: 'cheerleaders', label: '啦啦队', emoji: '💃' },
                { id: 'tourism', label: '文旅推广', emoji: '🏛️' }
              ].map(item => (
                <button
                  key={item.id}
                  onClick={() => setCurrentView(item.id)}
                  style={{
                    padding: '8px 16px',
                    borderRadius: '8px',
                    border: 'none',
                    background: currentView === item.id ? '#dbeafe' : 'transparent',
                    color: currentView === item.id ? '#1d4ed8' : '#6b7280',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => {
                    if (currentView !== item.id) {
                      e.target.style.background = '#f3f4f6';
                    }
                  }}
                  onMouseOut={(e) => {
                    if (currentView !== item.id) {
                      e.target.style.background = 'transparent';
                    }
                  }}
                >
                  {item.emoji} {item.label}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </header>

      <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '1.5rem' }}>
        {/* 搞笑标题区域 */}
        <div style={{ textAlign: 'center', padding: '2rem 0' }}>
          <h1 style={{
            fontSize: '3rem',
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #2563eb, #7c3aed)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            marginBottom: '1rem'
          }}>
            假如我是常州 🤔
          </h1>
          <p style={{ fontSize: '1.25rem', color: '#6b7280', marginBottom: '0.5rem' }}>
            足球管理 × 文旅推广 × 城市营销 × 搞笑日常
          </p>
          <p style={{ color: '#9ca3af', fontSize: '1rem' }}>
            带领常州队征战苏超，顺便推广一下龙城文化（如果我们能赢球的话...）
          </p>

          {/* 搞笑语录展示 */}
          <div style={{
            background: 'white',
            borderRadius: '12px',
            padding: '1.5rem',
            margin: '2rem auto',
            maxWidth: '600px',
            boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
            border: '2px dashed #fbbf24'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
              <span style={{ fontSize: '1.5rem' }}>💬</span>
              <h3 style={{ fontSize: '1.1rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
                今日搞笑语录
              </h3>
            </div>
            <p style={{
              fontSize: '1rem',
              color: '#374151',
              fontStyle: 'italic',
              lineHeight: '1.6',
              margin: '0 0 1rem 0'
            }}>
              "{currentQuote}"
            </p>
            <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'center', flexWrap: 'wrap' }}>
              <button
                onClick={refreshQuote}
                style={{
                  background: '#fbbf24',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  fontWeight: '500'
                }}
                onMouseOver={(e) => e.target.style.background = '#f59e0b'}
                onMouseOut={(e) => e.target.style.background = '#fbbf24'}
              >
                🎲 换一句
              </button>

              <button
                onClick={triggerRandomDialog}
                style={{
                  background: '#8b5cf6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  fontWeight: '500'
                }}
                onMouseOver={(e) => e.target.style.background = '#7c3aed'}
                onMouseOut={(e) => e.target.style.background = '#8b5cf6'}
              >
                💬 随机对话
              </button>

              <button
                onClick={triggerTestAchievement}
                style={{
                  background: '#ef4444',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  fontWeight: '500'
                }}
                onMouseOver={(e) => e.target.style.background = '#dc2626'}
                onMouseOut={(e) => e.target.style.background = '#ef4444'}
              >
                🏆 测试成就
              </button>
            </div>
          </div>
        </div>

        {/* 搞笑功能卡片 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '1.5rem',
          marginTop: '2rem'
        }}>
          {[
            {
              emoji: '⚽',
              title: '球队管理',
              subtitle: '（试图管理）',
              description: '管理常州队阵容、战术和训练',
              funnyNote: '注意：球员可能会罢工要求加薪',
              bgColor: '#dbeafe',
              onClick: () => setCurrentView('team')
            },
            {
              emoji: '🏆',
              title: '比赛中心',
              subtitle: '（希望能赢）',
              description: '安排比赛，观看精彩对决',
              funnyNote: '温馨提示：准备好纸巾擦眼泪',
              bgColor: '#fef3c7',
              onClick: () => setCurrentView('match')
            },
            {
              emoji: '💃',
              title: '啦啦队管理',
              subtitle: '（颜值担当）',
              description: '组建啦啦队，安排精彩表演',
              funnyNote: '她们比球员更受欢迎...',
              bgColor: '#fce7f3',
              onClick: () => setCurrentView('cheerleaders')
            },
            {
              emoji: '🏛️',
              title: '文旅推广',
              subtitle: '（赚外快）',
              description: '推广常州景点和特色文化',
              funnyNote: '毕竟踢球不赚钱，得想办法恰饭',
              bgColor: '#d1fae5',
              onClick: () => setCurrentView('tourism')
            }
          ].map((card, index) => (
            <div
              key={index}
              onClick={card.onClick}
              style={{
                background: 'white',
                borderRadius: '16px',
                padding: '1.5rem',
                boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                border: '2px solid transparent'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                e.currentTarget.style.borderColor = '#3b82f6';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
                e.currentTarget.style.borderColor = 'transparent';
              }}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  width: '64px',
                  height: '64px',
                  background: card.bgColor,
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 1rem auto',
                  fontSize: '2rem'
                }}>
                  {card.emoji}
                </div>
                <h3 style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  marginBottom: '0.25rem',
                  color: '#1f2937'
                }}>
                  {card.title} <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>{card.subtitle}</span>
                </h3>
                <p style={{
                  color: '#6b7280',
                  fontSize: '0.875rem',
                  marginBottom: '0.75rem',
                  lineHeight: '1.4'
                }}>
                  {card.description}
                </p>
                <p style={{
                  color: '#f59e0b',
                  fontSize: '0.75rem',
                  fontStyle: 'italic',
                  background: '#fffbeb',
                  padding: '0.5rem',
                  borderRadius: '6px',
                  margin: 0
                }}>
                  💡 {card.funnyNote}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* 搞笑常州特色展示 */}
        <div style={{
          marginTop: '3rem',
          background: 'white',
          borderRadius: '16px',
          padding: '2rem',
          boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: '1.5rem',
            color: '#1f2937'
          }}>
            常州文化特色 <span style={{ fontSize: '1rem', color: '#6b7280' }}>（我们的卖点）</span>
          </h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1.5rem'
          }}>
            {[
              {
                emoji: '🦕',
                title: '中华恐龙园',
                subtitle: '恐龙主题乐园',
                funnyDesc: '比我们球队历史还悠久的恐龙们',
                popularity: 95
              },
              {
                emoji: '🏯',
                title: '天宁寺',
                subtitle: '千年古刹',
                funnyDesc: '球迷经常来这里祈祷我们能赢球',
                popularity: 85
              },
              {
                emoji: '🌸',
                title: '红梅公园',
                subtitle: '梅花胜地',
                funnyDesc: '春天赏花，秋天看我们掉眼泪',
                popularity: 80
              },
              {
                emoji: '🍜',
                title: '银丝面',
                subtitle: '常州美食',
                funnyDesc: '比我们的传球还要细腻顺滑',
                popularity: 90
              },
              {
                emoji: '🎭',
                title: '淹城春秋乐园',
                subtitle: '春秋文化',
                funnyDesc: '学习古人智慧，希望能用在足球上',
                popularity: 75
              },
              {
                emoji: '🏔️',
                title: '天目湖',
                subtitle: '湖光山色',
                funnyDesc: '输球后队员们经常来这里冷静',
                popularity: 88
              }
            ].map((item, index) => (
              <div
                key={index}
                style={{
                  textAlign: 'center',
                  padding: '1rem',
                  borderRadius: '12px',
                  background: '#f9fafb',
                  border: '1px solid #e5e7eb',
                  transition: 'all 0.3s ease'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.background = '#f3f4f6';
                  e.currentTarget.style.transform = 'scale(1.02)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.background = '#f9fafb';
                  e.currentTarget.style.transform = 'scale(1)';
                }}
              >
                <div style={{ fontSize: '3rem', marginBottom: '0.5rem' }}>{item.emoji}</div>
                <h4 style={{
                  fontWeight: '600',
                  marginBottom: '0.25rem',
                  color: '#1f2937',
                  fontSize: '1rem'
                }}>
                  {item.title}
                </h4>
                <p style={{
                  fontSize: '0.75rem',
                  color: '#6b7280',
                  marginBottom: '0.5rem'
                }}>
                  {item.subtitle}
                </p>
                <p style={{
                  fontSize: '0.75rem',
                  color: '#f59e0b',
                  fontStyle: 'italic',
                  lineHeight: '1.3',
                  marginBottom: '0.5rem'
                }}>
                  {item.funnyDesc}
                </p>
                <div style={{
                  background: '#dbeafe',
                  borderRadius: '4px',
                  padding: '0.25rem 0.5rem',
                  fontSize: '0.75rem',
                  color: '#1d4ed8',
                  fontWeight: '500'
                }}>
                  人气值: {item.popularity}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 搞笑状态栏 */}
        <div style={{
          marginTop: '3rem',
          background: 'linear-gradient(45deg, #fef3c7, #fde68a)',
          borderRadius: '16px',
          padding: '1.5rem',
          border: '2px solid #f59e0b'
        }}>
          <h3 style={{
            fontSize: '1.25rem',
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: '1rem',
            color: '#92400e'
          }}>
            🎮 游戏状态面板 🎮
          </h3>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: '1rem',
            textAlign: 'center'
          }}>
            <div>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>💰</div>
              <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>预算</div>
              <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>{gameStats.budget}万</div>
              <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>（还够买几包辣条）</div>
            </div>

            <div>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>⭐</div>
              <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>声望</div>
              <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>{gameStats.reputation}</div>
              <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>（勉强及格水平）</div>
            </div>

            <div>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>👥</div>
              <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>粉丝</div>
              <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>{gameStats.fans}万</div>
              <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>（大部分是亲戚朋友）</div>
            </div>

            <div>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>🏆</div>
              <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>胜率</div>
              <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>{gameStats.winRate}%</div>
              <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>（至少不是0%...）</div>
            </div>

            <div>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>😭</div>
              <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>球迷心情</div>
              <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>{gameStats.mood}</div>
              <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>（爱恨交织中）</div>
            </div>
          </div>
        </div>
      </main>

      {/* 搞笑浮动提示 */}
      <div style={{
        position: 'fixed',
        bottom: '1rem',
        right: '1rem',
        zIndex: 50
      }}>
        <div style={{
          background: 'rgba(255,255,255,0.95)',
          backdropFilter: 'blur(8px)',
          borderRadius: '12px',
          padding: '0.75rem',
          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
          border: '2px solid #e5e7eb',
          maxWidth: '200px'
        }}>
          <h3 style={{
            fontSize: '0.875rem',
            fontWeight: '600',
            color: '#2563eb',
            margin: '0 0 0.25rem 0'
          }}>
            🎯 假如我是常州
          </h3>
          <p style={{
            fontSize: '0.75rem',
            color: '#6b7280',
            margin: 0,
            lineHeight: '1.3'
          }}>
            足球管理 × 文旅推广<br/>
            <span style={{ color: '#f59e0b', fontStyle: 'italic' }}>
              专业搞笑30年 😄
            </span>
          </p>
        </div>
      </div>

      {/* 对话系统 */}
      <DialogSystem
        isOpen={showDialog}
        onClose={() => setShowDialog(false)}
        onEffect={handleDialogEffect}
      />

      {/* 随机事件系统 */}
      <RandomEvents onEventTrigger={handleRandomEvent} />

      {/* 成就系统 */}
      <AchievementSystem
        gameStats={gameStats}
        onAchievementUnlock={handleAchievementUnlock}
      />
    </div>
  );
}

export default App;
