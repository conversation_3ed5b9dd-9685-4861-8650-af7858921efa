import React, { useState } from 'react';
import { useGameStore } from '../store/gameStore';
import { 
  MapPin, 
  ShoppingBag, 
  TrendingUp, 
  Star,
  Plus,
  Eye,
  DollarSign,
  Calendar
} from 'lucide-react';

type TabType = 'attractions' | 'merchandise' | 'promotions' | 'analytics';

const TourismPromotion: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('attractions');
  const { 
    attractions, 
    merchandise, 
    activePromotions,
    budget,
    reputation,
    promoteAttraction,
    launchMerchandise 
  } = useGameStore();

  const tabs = [
    { id: 'attractions', label: '景点推广', icon: MapPin },
    { id: 'merchandise', label: '特色商品', icon: ShoppingBag },
    { id: 'promotions', label: '推广活动', icon: Calendar },
    { id: 'analytics', label: '数据分析', icon: TrendingUp },
  ] as const;

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'historical': return '🏛️';
      case 'cultural': return '🎭';
      case 'natural': return '🌿';
      case 'modern': return '🏢';
      case 'jersey': return '👕';
      case 'scarf': return '🧣';
      case 'food': return '🍜';
      case 'souvenir': return '🎁';
      default: return '⭐';
    }
  };

  const getPopularityColor = (popularity: number) => {
    if (popularity >= 90) return 'text-green-600 bg-green-50';
    if (popularity >= 75) return 'text-blue-600 bg-blue-50';
    if (popularity >= 60) return 'text-yellow-600 bg-yellow-50';
    return 'text-gray-600 bg-gray-50';
  };

  const renderAttractionsView = () => (
    <div className="space-y-6">
      {/* 景点概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card p-4 text-center">
          <MapPin className="w-8 h-8 text-primary-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">{attractions.length}</p>
          <p className="text-sm text-gray-600">总景点数</p>
        </div>
        <div className="card p-4 text-center">
          <Star className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">
            {Math.floor(attractions.reduce((sum, a) => sum + a.popularity, 0) / attractions.length)}
          </p>
          <p className="text-sm text-gray-600">平均人气</p>
        </div>
        <div className="card p-4 text-center">
          <TrendingUp className="w-8 h-8 text-green-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">
            {attractions.filter(a => a.promotionLevel > 5).length}
          </p>
          <p className="text-sm text-gray-600">高推广景点</p>
        </div>
        <div className="card p-4 text-center">
          <DollarSign className="w-8 h-8 text-purple-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">{budget.tourism}万</p>
          <p className="text-sm text-gray-600">推广预算</p>
        </div>
      </div>

      {/* 景点列表 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold mb-4">常州景点推广</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {attractions.map((attraction) => (
            <div key={attraction.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h4 className="font-semibold flex items-center">
                    <span className="mr-2">{getTypeIcon(attraction.type)}</span>
                    {attraction.name}
                  </h4>
                  <p className="text-sm text-gray-600 capitalize">{attraction.type}</p>
                </div>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getPopularityColor(attraction.popularity)}`}>
                  {attraction.popularity}
                </span>
              </div>
              
              <p className="text-sm text-gray-700 mb-3">{attraction.description}</p>
              
              <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                <div>
                  <p className="text-gray-600">门票价格</p>
                  <p className="font-semibold">{attraction.visitCost === 0 ? '免费' : `${attraction.visitCost}元`}</p>
                </div>
                <div>
                  <p className="text-gray-600">推广等级</p>
                  <div className="flex items-center">
                    {Array.from({ length: 10 }, (_, i) => (
                      <Star 
                        key={i} 
                        className={`w-3 h-3 ${i < attraction.promotionLevel ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                      />
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-2">
                <button 
                  onClick={() => promoteAttraction(attraction.id, 50)}
                  className="flex-1 btn-primary text-sm"
                  disabled={budget.tourism < 50}
                >
                  <TrendingUp className="w-3 h-3 mr-1" />
                  推广 (50万)
                </button>
                <button 
                  onClick={() => promoteAttraction(attraction.id, 100)}
                  className="flex-1 btn-secondary text-sm"
                  disabled={budget.tourism < 100}
                >
                  <Star className="w-3 h-3 mr-1" />
                  重点推广 (100万)
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderMerchandiseView = () => (
    <div className="space-y-6">
      {/* 商品概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card p-4 text-center">
          <ShoppingBag className="w-8 h-8 text-primary-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">{merchandise.length}</p>
          <p className="text-sm text-gray-600">商品种类</p>
        </div>
        <div className="card p-4 text-center">
          <Star className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">
            {merchandise.filter(m => m.isChangzhouSpecial).length}
          </p>
          <p className="text-sm text-gray-600">常州特色</p>
        </div>
        <div className="card p-4 text-center">
          <DollarSign className="w-8 h-8 text-green-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">
            {Math.floor(merchandise.reduce((sum, m) => sum + (m.price - m.cost) * m.stock, 0) / 10000)}万
          </p>
          <p className="text-sm text-gray-600">潜在收益</p>
        </div>
        <div className="card p-4 text-center">
          <Eye className="w-8 h-8 text-purple-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">
            {Math.floor(merchandise.reduce((sum, m) => sum + m.popularity, 0) / merchandise.length)}
          </p>
          <p className="text-sm text-gray-600">平均人气</p>
        </div>
      </div>

      {/* 商品列表 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold mb-4">特色商品管理</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {merchandise.map((item) => (
            <div key={item.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h4 className="font-semibold flex items-center">
                    <span className="mr-2">{getTypeIcon(item.type)}</span>
                    {item.name}
                  </h4>
                  <div className="flex items-center space-x-2">
                    <p className="text-sm text-gray-600 capitalize">{item.type}</p>
                    {item.isChangzhouSpecial && (
                      <span className="px-2 py-1 bg-primary-100 text-primary-600 rounded text-xs font-medium">
                        常州特色
                      </span>
                    )}
                  </div>
                </div>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getPopularityColor(item.popularity)}`}>
                  {item.popularity}
                </span>
              </div>
              
              <p className="text-sm text-gray-700 mb-3">{item.description}</p>
              
              <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                <div>
                  <p className="text-gray-600">售价</p>
                  <p className="font-semibold text-green-600">{item.price}元</p>
                </div>
                <div>
                  <p className="text-gray-600">成本</p>
                  <p className="font-semibold text-red-600">{item.cost}元</p>
                </div>
                <div>
                  <p className="text-gray-600">库存</p>
                  <p className="font-semibold">{item.stock}件</p>
                </div>
                <div>
                  <p className="text-gray-600">利润</p>
                  <p className="font-semibold text-blue-600">{item.price - item.cost}元</p>
                </div>
              </div>
              
              <div className="flex space-x-2">
                <button 
                  onClick={() => launchMerchandise(item.id, 50)}
                  className="flex-1 btn-primary text-sm"
                  disabled={budget.tourism < item.cost * 50}
                >
                  <Plus className="w-3 h-3 mr-1" />
                  补货50件
                </button>
                <button 
                  onClick={() => launchMerchandise(item.id, 100)}
                  className="flex-1 btn-secondary text-sm"
                  disabled={budget.tourism < item.cost * 100}
                >
                  <ShoppingBag className="w-3 h-3 mr-1" />
                  大量补货
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderPromotionsView = () => (
    <div className="space-y-6">
      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">推广活动</h3>
          <button className="btn-primary">
            <Plus className="w-4 h-4 mr-2" />
            创建活动
          </button>
        </div>
        
        <div className="text-center py-12 text-gray-500">
          <Calendar className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg">暂无推广活动</p>
          <p className="text-sm">创建文旅推广活动来提升常州知名度</p>
        </div>
      </div>
    </div>
  );

  const renderAnalyticsView = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 景点分析 */}
        <div className="card p-6">
          <h4 className="font-semibold mb-4">景点类型分布</h4>
          <div className="space-y-3">
            {['historical', 'cultural', 'natural', 'modern'].map((type) => {
              const count = attractions.filter(a => a.type === type).length;
              const percentage = (count / attractions.length) * 100;
              return (
                <div key={type} className="flex justify-between items-center">
                  <span className="text-sm capitalize flex items-center">
                    <span className="mr-2">{getTypeIcon(type)}</span>
                    {type}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full" 
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-semibold">{count}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        
        {/* 商品分析 */}
        <div className="card p-6">
          <h4 className="font-semibold mb-4">商品类型分布</h4>
          <div className="space-y-3">
            {['jersey', 'scarf', 'food', 'souvenir', 'cultural'].map((type) => {
              const items = merchandise.filter(m => m.type === type);
              const count = items.length;
              const totalValue = items.reduce((sum, m) => sum + m.price * m.stock, 0);
              return (
                <div key={type} className="flex justify-between items-center">
                  <span className="text-sm capitalize flex items-center">
                    <span className="mr-2">{getTypeIcon(type)}</span>
                    {type}
                  </span>
                  <div className="text-right">
                    <p className="text-sm font-semibold">{count}种</p>
                    <p className="text-xs text-gray-500">{Math.floor(totalValue / 10000)}万价值</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        
        {/* 收益分析 */}
        <div className="card p-6">
          <h4 className="font-semibold mb-4">收益预测</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm">景点门票收入</span>
              <span className="font-semibold text-green-600">
                {Math.floor(attractions.reduce((sum, a) => sum + a.visitCost * a.popularity, 0) / 10000)}万/月
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">商品销售收入</span>
              <span className="font-semibold text-blue-600">
                {Math.floor(merchandise.reduce((sum, m) => sum + (m.price - m.cost) * m.popularity, 0) / 10000)}万/月
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">推广投入</span>
              <span className="font-semibold text-red-600">
                {Math.floor(attractions.reduce((sum, a) => sum + a.promotionLevel * 10, 0) / 10000)}万/月
              </span>
            </div>
            <div className="pt-2 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <span className="text-sm font-semibold">净收益预估</span>
                <span className="font-bold text-primary-600">
                  {Math.floor((
                    attractions.reduce((sum, a) => sum + a.visitCost * a.popularity, 0) +
                    merchandise.reduce((sum, m) => sum + (m.price - m.cost) * m.popularity, 0) -
                    attractions.reduce((sum, a) => sum + a.promotionLevel * 10, 0)
                  ) / 10000)}万/月
                </span>
              </div>
            </div>
          </div>
        </div>
        
        {/* 声望影响 */}
        <div className="card p-6">
          <h4 className="font-semibold mb-4">声望影响</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm">文化声望</span>
              <span className="font-semibold text-purple-600">{reputation.cultural}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">旅游声望</span>
              <span className="font-semibold text-green-600">{reputation.tourism}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">综合影响</span>
              <span className="font-semibold text-primary-600">
                +{Math.floor((reputation.cultural + reputation.tourism) / 20)}% 粉丝增长
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'attractions':
        return renderAttractionsView();
      case 'merchandise':
        return renderMerchandiseView();
      case 'promotions':
        return renderPromotionsView();
      case 'analytics':
        return renderAnalyticsView();
      default:
        return renderAttractionsView();
    }
  };

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">文旅推广</h2>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <span>景点: {attractions.length}个</span>
          <span>商品: {merchandise.length}种</span>
          <span>预算: {budget.tourism}万</span>
        </div>
      </div>

      {/* 标签导航 */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* 内容区域 */}
      {renderContent()}
    </div>
  );
};

export default TourismPromotion;
