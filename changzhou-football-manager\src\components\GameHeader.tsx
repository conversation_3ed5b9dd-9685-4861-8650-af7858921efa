import React from 'react';
import { useGameStore } from '../store/gameStore';
import { 
  Trophy, 
  Users, 
  Calendar, 
  Heart, 
  MapPin, 
  BarChart3,
  Coins,
  Star
} from 'lucide-react';

type GameView = 'dashboard' | 'team' | 'match' | 'cheerleaders' | 'tourism';

interface GameHeaderProps {
  currentView: GameView;
  onViewChange: (view: GameView) => void;
}

const GameHeader: React.FC<GameHeaderProps> = ({ currentView, onViewChange }) => {
  const { 
    playerTeam, 
    budget, 
    reputation, 
    gameDate,
    currentSeason 
  } = useGameStore();

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(0)}万`;
  };

  const navigationItems = [
    { id: 'dashboard', label: '总览', icon: BarChart3 },
    { id: 'team', label: '球队管理', icon: Users },
    { id: 'match', label: '比赛中心', icon: Trophy },
    { id: 'cheerleaders', label: '啦啦队', icon: Heart },
    { id: 'tourism', label: '文旅推广', icon: MapPin },
  ] as const;

  return (
    <header className="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-40">
      {/* 顶部信息栏 */}
      <div className="bg-primary-600 text-white py-2">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(gameDate)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>第{currentSeason.currentRound}轮</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Coins className="w-4 h-4" />
                <span>总预算: {formatCurrency(budget.total)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Star className="w-4 h-4" />
                <span>声望: {reputation.overall}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主导航栏 */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between py-4">
          {/* 球队信息 */}
          <div className="flex items-center space-x-4">
            <div className="team-badge">
              常
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">{playerTeam.name}</h1>
              <p className="text-sm text-gray-600">
                {playerTeam.city} | {playerTeam.stadium}
              </p>
            </div>
          </div>

          {/* 导航菜单 */}
          <nav className="flex space-x-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentView === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => onViewChange(item.id as GameView)}
                  className={`
                    flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
                    ${isActive 
                      ? 'bg-primary-100 text-primary-700 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }
                  `}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.label}</span>
                </button>
              );
            })}
          </nav>

          {/* 快速状态 */}
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm text-gray-600">球员数量</div>
              <div className="font-semibold">{playerTeam.players.length}</div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">粉丝数量</div>
              <div className="font-semibold">{(playerTeam.fanBase / 10000).toFixed(1)}万</div>
            </div>
          </div>
        </div>
      </div>

      {/* 预算详情栏 */}
      <div className="bg-gray-50 border-t border-gray-200">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex space-x-8">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
                <span className="text-sm text-gray-600">足球预算:</span>
                <span className="font-semibold text-primary-600">{formatCurrency(budget.football)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                <span className="text-sm text-gray-600">啦啦队:</span>
                <span className="font-semibold text-pink-600">{formatCurrency(budget.cheerleaders)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">文旅:</span>
                <span className="font-semibold text-green-600">{formatCurrency(budget.tourism)}</span>
              </div>
            </div>
            
            <div className="flex space-x-6">
              <div className="text-center">
                <div className="text-xs text-gray-500">足球声望</div>
                <div className="font-semibold text-primary-600">{reputation.football}</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-500">文化声望</div>
                <div className="font-semibold text-purple-600">{reputation.cultural}</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-500">旅游声望</div>
                <div className="font-semibold text-green-600">{reputation.tourism}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default GameHeader;
