@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gradient-to-br from-slate-50 to-blue-50 font-sans antialiased;
    margin: 0;
    min-height: 100vh;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-semibold;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-md hover:shadow-lg;
  }

  .card {
    @apply bg-white rounded-xl shadow-card border border-gray-200 overflow-hidden;
  }

  .formation-grid {
    @apply grid gap-4 p-6 relative;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  }

  .player-position {
    @apply w-16 h-16 rounded-full bg-white shadow-lg flex items-center justify-center text-sm font-semibold text-gray-800 border-2 border-primary-500 cursor-pointer transition-all duration-200 hover:scale-110;
  }
}
