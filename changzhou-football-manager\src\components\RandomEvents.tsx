import React, { useState, useEffect } from 'react';

interface RandomEvent {
  id: string;
  title: string;
  description: string;
  emoji: string;
  type: 'good' | 'bad' | 'neutral' | 'funny';
  effects: {
    budget?: number;
    reputation?: number;
    fans?: number;
    mood?: string;
  };
}

// 搞笑随机事件库
const randomEvents: RandomEvent[] = [
  {
    id: 'dinosaur_visit',
    title: '恐龙园恐龙来访',
    description: '中华恐龙园的吉祥物恐龙来球队参观，球员们被吓得四处逃窜，但观众觉得很有趣！',
    emoji: '🦕',
    type: 'funny',
    effects: {
      fans: 2,
      reputation: 1,
      mood: '惊恐但开心'
    }
  },
  {
    id: 'noodle_sponsor',
    title: '银丝面赞助',
    description: '常州银丝面厂决定赞助球队！条件是球员们要在广告中表演吃面条，结果全都被噎到了。',
    emoji: '🍜',
    type: 'good',
    effects: {
      budget: 50,
      reputation: -1,
      mood: '饱腹但尴尬'
    }
  },
  {
    id: 'player_lost',
    title: '球员迷路事件',
    description: '前锋小李在去训练的路上迷路了，最后在恐龙园里被发现，说是想学恐龙的跑步姿势。',
    emoji: '🤦‍♂️',
    type: 'funny',
    effects: {
      reputation: -1,
      fans: 1,
      mood: '无语但好笑'
    }
  },
  {
    id: 'cheerleader_viral',
    title: '啦啦队走红',
    description: '啦啦队的一个搞笑视频在网上疯传，虽然是因为集体摔倒，但意外获得了大量关注！',
    emoji: '📱',
    type: 'good',
    effects: {
      fans: 5,
      reputation: 2,
      mood: '疼痛但出名'
    }
  },
  {
    id: 'coach_wisdom',
    title: '教练金句',
    description: '教练在采访中说："我们不是最强的，但我们是最努力的...努力搞笑。"这句话成为了网络热梗。',
    emoji: '🎤',
    type: 'good',
    effects: {
      fans: 3,
      reputation: 1,
      mood: '自豪但羞耻'
    }
  },
  {
    id: 'equipment_malfunction',
    title: '设备故障',
    description: '训练时足球机器人故障，开始追着球员跑。球员们的逃跑速度创造了队史纪录！',
    emoji: '🤖',
    type: 'funny',
    effects: {
      reputation: -1,
      fans: 2,
      mood: '恐惧但快速'
    }
  },
  {
    id: 'fan_gift',
    title: '球迷送礼',
    description: '一位老球迷送来了自制的"必胜"护身符，虽然看起来像是从垃圾桶里捡的，但心意很珍贵。',
    emoji: '🎁',
    type: 'good',
    effects: {
      fans: 1,
      mood: '感动但困惑'
    }
  },
  {
    id: 'weather_chaos',
    title: '天气突变',
    description: '比赛时突然下大雨，球员们在泥地里滑来滑去，观众说这是他们看过最有趣的"泥浆摔跤"。',
    emoji: '🌧️',
    type: 'funny',
    effects: {
      fans: 2,
      reputation: -1,
      mood: '湿润但快乐'
    }
  },
  {
    id: 'mascot_accident',
    title: '吉祥物意外',
    description: '球队吉祥物（一只蓝色的龙）在表演时头套掉了，露出了里面的大叔，小朋友们都哭了。',
    emoji: '🐉',
    type: 'bad',
    effects: {
      fans: -1,
      reputation: -1,
      mood: '尴尬且内疚'
    }
  },
  {
    id: 'food_poisoning',
    title: '集体拉肚子',
    description: '球员们吃了过期的银丝面，集体拉肚子。比赛时频繁跑厕所，创造了"最多暂停"记录。',
    emoji: '🚽',
    type: 'bad',
    effects: {
      reputation: -2,
      fans: 1,
      mood: '痛苦但搞笑'
    }
  }
];

interface RandomEventsProps {
  onEventTrigger?: (event: RandomEvent) => void;
}

const RandomEvents: React.FC<RandomEventsProps> = ({ onEventTrigger }) => {
  const [currentEvent, setCurrentEvent] = useState<RandomEvent | null>(null);
  const [showEvent, setShowEvent] = useState(false);

  useEffect(() => {
    // 每30秒有20%的概率触发随机事件
    const interval = setInterval(() => {
      if (Math.random() < 0.2 && !showEvent) {
        triggerRandomEvent();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [showEvent]);

  const triggerRandomEvent = () => {
    const randomEvent = randomEvents[Math.floor(Math.random() * randomEvents.length)];
    setCurrentEvent(randomEvent);
    setShowEvent(true);
    
    if (onEventTrigger) {
      onEventTrigger(randomEvent);
    }
  };

  const handleEventClose = () => {
    setShowEvent(false);
    setCurrentEvent(null);
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case 'good': return { bg: '#dcfce7', border: '#16a34a', text: '#15803d' };
      case 'bad': return { bg: '#fee2e2', border: '#dc2626', text: '#dc2626' };
      case 'funny': return { bg: '#fef3c7', border: '#f59e0b', text: '#d97706' };
      default: return { bg: '#f3f4f6', border: '#6b7280', text: '#374151' };
    }
  };

  if (!showEvent || !currentEvent) return null;

  const colors = getEventColor(currentEvent.type);

  return (
    <div style={{
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      zIndex: 1000,
      background: 'white',
      borderRadius: '16px',
      padding: '2rem',
      maxWidth: '400px',
      width: '90%',
      boxShadow: '0 20px 25px rgba(0,0,0,0.1)',
      border: `3px solid ${colors.border}`,
      animation: 'bounce 0.5s ease-out'
    }}>
      {/* 事件标题 */}
      <div style={{
        textAlign: 'center',
        marginBottom: '1.5rem'
      }}>
        <div style={{
          fontSize: '4rem',
          marginBottom: '0.5rem'
        }}>
          {currentEvent.emoji}
        </div>
        <h3 style={{
          fontSize: '1.25rem',
          fontWeight: 'bold',
          color: colors.text,
          margin: 0
        }}>
          突发事件！
        </h3>
        <h4 style={{
          fontSize: '1.125rem',
          fontWeight: '600',
          color: '#1f2937',
          margin: '0.5rem 0 0 0'
        }}>
          {currentEvent.title}
        </h4>
      </div>

      {/* 事件描述 */}
      <div style={{
        background: colors.bg,
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '1.5rem',
        border: `2px solid ${colors.border}`
      }}>
        <p style={{
          fontSize: '1rem',
          lineHeight: '1.6',
          color: '#374151',
          margin: 0
        }}>
          {currentEvent.description}
        </p>
      </div>

      {/* 事件效果 */}
      <div style={{
        background: '#f9fafb',
        borderRadius: '8px',
        padding: '1rem',
        marginBottom: '1.5rem'
      }}>
        <h5 style={{
          fontSize: '0.875rem',
          fontWeight: '600',
          color: '#6b7280',
          margin: '0 0 0.5rem 0'
        }}>
          事件影响：
        </h5>
        <div style={{ fontSize: '0.875rem', color: '#374151' }}>
          {Object.entries(currentEvent.effects).map(([key, value]) => (
            <div key={key} style={{ marginBottom: '0.25rem' }}>
              {key === 'budget' && `💰 预算: ${value > 0 ? '+' : ''}${value}万`}
              {key === 'reputation' && `⭐ 声望: ${value > 0 ? '+' : ''}${value}`}
              {key === 'fans' && `👥 粉丝: ${value > 0 ? '+' : ''}${value}万`}
              {key === 'mood' && `😊 球迷心情: ${value}`}
            </div>
          ))}
        </div>
      </div>

      {/* 关闭按钮 */}
      <div style={{ textAlign: 'center' }}>
        <button
          onClick={handleEventClose}
          style={{
            padding: '0.75rem 2rem',
            background: colors.border,
            border: 'none',
            borderRadius: '8px',
            color: 'white',
            fontSize: '1rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
          onMouseOver={(e) => {
            e.target.style.transform = 'scale(1.05)';
            e.target.style.opacity = '0.9';
          }}
          onMouseOut={(e) => {
            e.target.style.transform = 'scale(1)';
            e.target.style.opacity = '1';
          }}
        >
          😄 哈哈哈，继续游戏！
        </button>
      </div>

      <style>
        {`
          @keyframes bounce {
            0% { transform: translate(-50%, -50%) scale(0.3); opacity: 0; }
            50% { transform: translate(-50%, -50%) scale(1.05); }
            70% { transform: translate(-50%, -50%) scale(0.9); }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
          }
        `}
      </style>
    </div>
  );
};

export default RandomEvents;
