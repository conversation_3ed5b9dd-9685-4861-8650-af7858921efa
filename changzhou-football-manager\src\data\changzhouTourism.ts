import { TouristAttraction, Merchandise, CheerleaderMember, Performance } from '../types/game';

// 常州旅游景点
export const changzhouAttractions: TouristAttraction[] = [
  {
    id: 'cz-attr-001',
    name: '中华恐龙园',
    type: 'modern',
    description: '以恐龙为主题的大型游乐园，是常州最著名的旅游景点之一',
    popularity: 95,
    visitCost: 280,
    promotionLevel: 8
  },
  {
    id: 'cz-attr-002',
    name: '天宁寺',
    type: 'historical',
    description: '始建于唐代的千年古刹，拥有世界最高的佛塔',
    popularity: 85,
    visitCost: 80,
    promotionLevel: 7
  },
  {
    id: 'cz-attr-003',
    name: '红梅公园',
    type: 'natural',
    description: '常州市区最大的综合性公园，春季梅花盛开美不胜收',
    popularity: 80,
    visitCost: 0,
    promotionLevel: 6
  },
  {
    id: 'cz-attr-004',
    name: '淹城春秋乐园',
    type: 'cultural',
    description: '以春秋文化为主题的文化园区，展现2500年前的春秋风貌',
    popularity: 75,
    visitCost: 200,
    promotionLevel: 6
  },
  {
    id: 'cz-attr-005',
    name: '天目湖',
    type: 'natural',
    description: '风景秀丽的湖泊景区，有"江南明珠"之称',
    popularity: 88,
    visitCost: 180,
    promotionLevel: 7
  },
  {
    id: 'cz-attr-006',
    name: '溧阳南山竹海',
    type: 'natural',
    description: '万亩竹海，空气清新，是天然氧吧',
    popularity: 82,
    visitCost: 120,
    promotionLevel: 6
  },
  {
    id: 'cz-attr-007',
    name: '常州博物馆',
    type: 'cultural',
    description: '展示常州历史文化的综合性博物馆',
    popularity: 70,
    visitCost: 0,
    promotionLevel: 5
  },
  {
    id: 'cz-attr-008',
    name: '青枫公园',
    type: 'natural',
    description: '现代化城市公园，秋季枫叶红遍',
    popularity: 65,
    visitCost: 0,
    promotionLevel: 4
  },
  {
    id: 'cz-attr-009',
    name: '嬉戏谷',
    type: 'modern',
    description: '动漫游戏主题乐园，深受年轻人喜爱',
    popularity: 78,
    visitCost: 230,
    promotionLevel: 6
  },
  {
    id: 'cz-attr-010',
    name: '篦箕巷',
    type: 'historical',
    description: '明清古街，保存完好的传统建筑群',
    popularity: 72,
    visitCost: 0,
    promotionLevel: 5
  }
];

// 常州特色商品
export const changzhouMerchandise: Merchandise[] = [
  // 球队周边
  {
    id: 'cz-merch-001',
    name: '常州队主场球衣',
    type: 'jersey',
    price: 299,
    cost: 120,
    popularity: 90,
    stock: 500,
    description: '常州队2024赛季主场球衣，蓝金配色经典设计',
    isChangzhouSpecial: true
  },
  {
    id: 'cz-merch-002',
    name: '常州队围巾',
    type: 'scarf',
    price: 89,
    cost: 35,
    popularity: 85,
    stock: 800,
    description: '常州队官方围巾，柔软保暖，球迷必备',
    isChangzhouSpecial: true
  },
  {
    id: 'cz-merch-003',
    name: '常州队纪念徽章',
    type: 'souvenir',
    price: 25,
    cost: 8,
    popularity: 75,
    stock: 1000,
    description: '精美金属徽章，收藏纪念佳品',
    isChangzhouSpecial: true
  },

  // 常州特色美食
  {
    id: 'cz-merch-004',
    name: '常州银丝面',
    type: 'food',
    price: 35,
    cost: 12,
    popularity: 95,
    stock: 200,
    description: '常州传统名面，细如银丝，口感爽滑',
    isChangzhouSpecial: true
  },
  {
    id: 'cz-merch-005',
    name: '常州大麻糕',
    type: 'food',
    price: 45,
    cost: 18,
    popularity: 88,
    stock: 150,
    description: '常州传统糕点，香甜可口，老少皆宜',
    isChangzhouSpecial: true
  },
  {
    id: 'cz-merch-006',
    name: '溧阳白茶',
    type: 'food',
    price: 168,
    cost: 80,
    popularity: 82,
    stock: 100,
    description: '溧阳特产白茶，清香淡雅，养生佳品',
    isChangzhouSpecial: true
  },
  {
    id: 'cz-merch-007',
    name: '天目湖砂锅鱼头',
    type: 'food',
    price: 128,
    cost: 60,
    popularity: 90,
    stock: 80,
    description: '天目湖特色美食，鲜美营养，回味无穷',
    isChangzhouSpecial: true
  },

  // 文化纪念品
  {
    id: 'cz-merch-008',
    name: '恐龙园毛绒玩具',
    type: 'souvenir',
    price: 88,
    cost: 35,
    popularity: 85,
    stock: 300,
    description: '中华恐龙园官方毛绒玩具，可爱萌趣',
    isChangzhouSpecial: true
  },
  {
    id: 'cz-merch-009',
    name: '天宁寺开光手串',
    type: 'cultural',
    price: 99,
    cost: 40,
    popularity: 78,
    stock: 200,
    description: '天宁寺开光手串，寓意平安吉祥',
    isChangzhouSpecial: true
  },
  {
    id: 'cz-merch-010',
    name: '春秋文化书签',
    type: 'cultural',
    price: 15,
    cost: 5,
    popularity: 70,
    stock: 500,
    description: '淹城春秋乐园文化书签，古韵悠长',
    isChangzhouSpecial: true
  },
  {
    id: 'cz-merch-011',
    name: '常州梳篦',
    type: 'cultural',
    price: 58,
    cost: 25,
    popularity: 75,
    stock: 150,
    description: '常州传统工艺品，精工细作，实用美观',
    isChangzhouSpecial: true
  },
  {
    id: 'cz-merch-012',
    name: '红梅公园明信片套装',
    type: 'souvenir',
    price: 20,
    cost: 6,
    popularity: 65,
    stock: 400,
    description: '红梅公园四季美景明信片，留住美好回忆',
    isChangzhouSpecial: true
  }
];

// 啦啦队成员
export const changzhouCheerleaders: CheerleaderMember[] = [
  {
    id: 'cz-cheer-001',
    name: '李雨萱',
    age: 22,
    position: 'captain',
    skills: {
      dance: 92,
      singing: 85,
      charisma: 95,
      energy: 90
    },
    salary: 8,
    contract: { startYear: 2024, endYear: 2026 }
  },
  {
    id: 'cz-cheer-002',
    name: '王思琪',
    age: 20,
    position: 'dancer',
    skills: {
      dance: 88,
      singing: 70,
      charisma: 85,
      energy: 92
    },
    salary: 6,
    contract: { startYear: 2024, endYear: 2025 }
  },
  {
    id: 'cz-cheer-003',
    name: '张美琳',
    age: 21,
    position: 'singer',
    skills: {
      dance: 75,
      singing: 95,
      charisma: 88,
      energy: 85
    },
    salary: 7,
    contract: { startYear: 2024, endYear: 2026 }
  },
  {
    id: 'cz-cheer-004',
    name: '陈小雅',
    age: 19,
    position: 'performer',
    skills: {
      dance: 85,
      singing: 80,
      charisma: 90,
      energy: 95
    },
    salary: 5,
    contract: { startYear: 2024, endYear: 2027 }
  },
  {
    id: 'cz-cheer-005',
    name: '刘诗涵',
    age: 23,
    position: 'dancer',
    skills: {
      dance: 90,
      singing: 75,
      charisma: 82,
      energy: 88
    },
    salary: 6.5,
    contract: { startYear: 2023, endYear: 2025 }
  },
  {
    id: 'cz-cheer-006',
    name: '赵欣怡',
    age: 20,
    position: 'performer',
    skills: {
      dance: 82,
      singing: 88,
      charisma: 92,
      energy: 90
    },
    salary: 6,
    contract: { startYear: 2024, endYear: 2026 }
  }
];

// 表演节目
export const cheerleaderPerformances: Performance[] = [
  {
    id: 'cz-perf-001',
    name: '龙城飞舞',
    type: 'dance',
    duration: 5,
    difficulty: 8,
    popularity: 90,
    cost: 2000,
    requirements: {
      minMembers: 4,
      requiredSkills: {
        dance: 85,
        charisma: 80,
        energy: 85
      }
    },
    description: '以常州龙城文化为主题的大型舞蹈表演'
  },
  {
    id: 'cz-perf-002',
    name: '恐龙园奇遇',
    type: 'dance',
    duration: 4,
    difficulty: 6,
    popularity: 85,
    cost: 1500,
    requirements: {
      minMembers: 3,
      requiredSkills: {
        dance: 75,
        charisma: 75,
        energy: 80
      }
    },
    description: '结合恐龙园元素的趣味舞蹈'
  },
  {
    id: 'cz-perf-003',
    name: '春秋战歌',
    type: 'song',
    duration: 3,
    difficulty: 7,
    popularity: 80,
    cost: 1000,
    requirements: {
      minMembers: 2,
      requiredSkills: {
        singing: 90,
        charisma: 85
      }
    },
    description: '以春秋文化为背景的激昂战歌'
  },
  {
    id: 'cz-perf-004',
    name: '天目湖之歌',
    type: 'song',
    duration: 4,
    difficulty: 5,
    popularity: 75,
    cost: 800,
    requirements: {
      minMembers: 1,
      requiredSkills: {
        singing: 80,
        charisma: 70
      }
    },
    description: '赞美天目湖美景的抒情歌曲'
  },
  {
    id: 'cz-perf-005',
    name: '胜利加油',
    type: 'cheer',
    duration: 2,
    difficulty: 4,
    popularity: 95,
    cost: 500,
    requirements: {
      minMembers: 6,
      requiredSkills: {
        energy: 90,
        charisma: 80
      }
    },
    description: '激励球队和球迷的经典加油表演'
  },
  {
    id: 'cz-perf-006',
    name: '竹海翻腾',
    type: 'acrobatics',
    duration: 6,
    difficulty: 9,
    popularity: 88,
    cost: 3000,
    requirements: {
      minMembers: 4,
      requiredSkills: {
        dance: 90,
        energy: 95,
        charisma: 85
      }
    },
    description: '以南山竹海为灵感的高难度杂技表演'
  }
];

// 常州文化元素
export const changzhouCulturalElements = {
  nicknames: ['龙城', '常州', '毗陵'],
  historicalPeriods: ['春秋', '战国', '三国', '明清'],
  famousPersons: ['苏东坡', '唐荆川', '赵翼', '瞿秋白'],
  culturalSymbols: ['恐龙', '梳篦', '乱针绣', '留青竹刻'],
  localDialect: {
    greetings: ['侬好', '吃过了伐'],
    expressions: ['蛮好', '老灵额', '交关好']
  },
  festivals: [
    { name: '梅花节', month: 3, description: '红梅公园梅花盛开时节' },
    { name: '恐龙节', month: 5, description: '中华恐龙园主题节庆' },
    { name: '竹海节', month: 7, description: '南山竹海避暑节' },
    { name: '菊花节', month: 10, description: '青枫公园菊花展' }
  ]
};
