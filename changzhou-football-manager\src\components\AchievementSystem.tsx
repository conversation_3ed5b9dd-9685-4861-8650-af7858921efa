import React, { useState, useEffect } from 'react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  emoji: string;
  condition: string;
  unlocked: boolean;
  funnyReward: string;
}

// 搞笑成就系统
const achievements: Achievement[] = [
  {
    id: 'first_loss',
    title: '首败达成',
    description: '恭喜你！成功输掉了第一场比赛',
    emoji: '😭',
    condition: '输掉第一场比赛',
    unlocked: false,
    funnyReward: '获得"专业陪跑"称号，球迷们表示很理解'
  },
  {
    id: 'budget_crisis',
    title: '破产边缘',
    description: '预算低于100万，但还在坚持',
    emoji: '💸',
    condition: '预算少于100万',
    unlocked: false,
    funnyReward: '解锁"吃土模式"，球员开始自带干粮训练'
  },
  {
    id: 'fan_loyalty',
    title: '死忠粉丝',
    description: '即使连败10场，粉丝数量还在增长',
    emoji: '❤️',
    condition: '连败但粉丝增长',
    unlocked: false,
    funnyReward: '球迷们说："我们不是来看足球的，我们是来看喜剧的"'
  },
  {
    id: 'cheerleader_star',
    title: '啦啦队明星',
    description: '啦啦队比球队更受欢迎',
    emoji: '⭐',
    condition: '啦啦队声望超过球队',
    unlocked: false,
    funnyReward: '观众开始只为看啦啦队表演而来，球员表示很受伤'
  },
  {
    id: 'tourism_master',
    title: '文旅大师',
    description: '成功让恐龙园的游客比球迷还多',
    emoji: '🦕',
    condition: '文旅收入超过足球收入',
    unlocked: false,
    funnyReward: '常州市政府邀请你当旅游大使，但拒绝让你管足球队'
  },
  {
    id: 'meme_legend',
    title: '表情包传说',
    description: '球队的搞笑瞬间成为网络热梗',
    emoji: '😂',
    condition: '触发10次搞笑事件',
    unlocked: false,
    funnyReward: '球员们开始收到表情包代言邀请，收入比踢球还高'
  },
  {
    id: 'survival_expert',
    title: '生存专家',
    description: '在苏超联赛中存活一个完整赛季',
    emoji: '🏆',
    condition: '完成一个赛季',
    unlocked: false,
    funnyReward: '获得"不死小强"称号，其他球队开始研究你们的生存秘诀'
  },
  {
    id: 'comedy_king',
    title: '喜剧之王',
    description: '让观众笑得比哭得还多',
    emoji: '🎭',
    condition: '娱乐价值达到最高',
    unlocked: false,
    funnyReward: '德云社邀请整个球队去说相声，薪水比踢球高10倍'
  }
];

interface AchievementSystemProps {
  gameStats: any;
  onAchievementUnlock?: (achievement: Achievement) => void;
}

const AchievementSystem: React.FC<AchievementSystemProps> = ({ gameStats, onAchievementUnlock }) => {
  const [unlockedAchievements, setUnlockedAchievements] = useState<Achievement[]>([]);
  const [showNotification, setShowNotification] = useState(false);
  const [currentAchievement, setCurrentAchievement] = useState<Achievement | null>(null);

  useEffect(() => {
    // 检查成就解锁条件
    achievements.forEach(achievement => {
      if (!achievement.unlocked && !unlockedAchievements.find(a => a.id === achievement.id)) {
        let shouldUnlock = false;

        switch (achievement.id) {
          case 'budget_crisis':
            shouldUnlock = gameStats.budget < 100;
            break;
          case 'fan_loyalty':
            shouldUnlock = gameStats.fans > 15; // 初始15万，如果增长了
            break;
          case 'cheerleader_star':
            shouldUnlock = gameStats.reputation < 70; // 球队声望下降但啦啦队受欢迎
            break;
          case 'tourism_master':
            shouldUnlock = gameStats.budget > 2000; // 通过文旅赚钱了
            break;
          default:
            break;
        }

        if (shouldUnlock) {
          unlockAchievement(achievement);
        }
      }
    });
  }, [gameStats, unlockedAchievements]);

  const unlockAchievement = (achievement: Achievement) => {
    const unlockedAchievement = { ...achievement, unlocked: true };
    setUnlockedAchievements(prev => [...prev, unlockedAchievement]);
    setCurrentAchievement(unlockedAchievement);
    setShowNotification(true);

    if (onAchievementUnlock) {
      onAchievementUnlock(unlockedAchievement);
    }

    // 3秒后自动关闭通知
    setTimeout(() => {
      setShowNotification(false);
      setCurrentAchievement(null);
    }, 5000);
  };

  const closeNotification = () => {
    setShowNotification(false);
    setCurrentAchievement(null);
  };

  return (
    <>
      {/* 成就通知 */}
      {showNotification && currentAchievement && (
        <div style={{
          position: 'fixed',
          top: '20px',
          right: '20px',
          zIndex: 1000,
          background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',
          borderRadius: '16px',
          padding: '1.5rem',
          maxWidth: '350px',
          boxShadow: '0 20px 25px rgba(0,0,0,0.1)',
          border: '3px solid #d97706',
          animation: 'bounce 0.5s ease-out'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '1rem'
          }}>
            <div style={{
              fontSize: '3rem',
              marginRight: '1rem'
            }}>
              {currentAchievement.emoji}
            </div>
            <div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: 'bold',
                color: 'white',
                margin: 0
              }}>
                🎉 成就解锁！
              </h3>
              <h4 style={{
                fontSize: '1.125rem',
                fontWeight: '600',
                color: '#fef3c7',
                margin: '0.25rem 0 0 0'
              }}>
                {currentAchievement.title}
              </h4>
            </div>
          </div>

          <p style={{
            fontSize: '0.875rem',
            color: 'white',
            marginBottom: '1rem',
            lineHeight: '1.4'
          }}>
            {currentAchievement.description}
          </p>

          <div style={{
            background: 'rgba(255,255,255,0.2)',
            borderRadius: '8px',
            padding: '0.75rem',
            marginBottom: '1rem'
          }}>
            <p style={{
              fontSize: '0.875rem',
              color: '#fef3c7',
              margin: 0,
              fontStyle: 'italic'
            }}>
              🎁 {currentAchievement.funnyReward}
            </p>
          </div>

          <button
            onClick={closeNotification}
            style={{
              width: '100%',
              padding: '0.5rem',
              background: 'rgba(255,255,255,0.2)',
              border: '2px solid white',
              borderRadius: '8px',
              color: 'white',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer'
            }}
          >
            😄 太棒了！
          </button>
        </div>
      )}

      {/* 成就列表按钮（可选，用于查看所有成就） */}
      <div style={{
        position: 'fixed',
        bottom: '80px',
        right: '20px',
        zIndex: 50
      }}>
        <button
          style={{
            width: '60px',
            height: '60px',
            borderRadius: '50%',
            background: 'linear-gradient(45deg, #8b5cf6, #7c3aed)',
            border: 'none',
            color: 'white',
            fontSize: '1.5rem',
            cursor: 'pointer',
            boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
            transition: 'all 0.2s'
          }}
          onMouseOver={(e) => {
            e.target.style.transform = 'scale(1.1)';
            e.target.style.boxShadow = '0 8px 15px rgba(0,0,0,0.2)';
          }}
          onMouseOut={(e) => {
            e.target.style.transform = 'scale(1)';
            e.target.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
          }}
          title={`已解锁成就: ${unlockedAchievements.length}/${achievements.length}`}
        >
          🏆
          <div style={{
            position: 'absolute',
            top: '-5px',
            right: '-5px',
            background: '#ef4444',
            color: 'white',
            borderRadius: '50%',
            width: '20px',
            height: '20px',
            fontSize: '0.75rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold'
          }}>
            {unlockedAchievements.length}
          </div>
        </button>
      </div>
    </>
  );
};

export default AchievementSystem;
