import React from 'react';
import { useGameStore } from '../store/gameStore';
import { 
  Trophy, 
  TrendingUp, 
  Users, 
  MapPin, 
  Calendar,
  Target,
  Award,
  Heart
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { 
    playerTeam, 
    budget, 
    reputation, 
    cheerleaders,
    attractions,
    merchandise,
    getNextMatch
  } = useGameStore();

  const nextMatch = getNextMatch();

  const stats = [
    {
      title: '球队总评',
      value: Math.floor(playerTeam.players.reduce((sum, p) => sum + p.overall, 0) / playerTeam.players.length),
      icon: Trophy,
      color: 'text-primary-600',
      bgColor: 'bg-primary-50',
      change: '+2'
    },
    {
      title: '总声望',
      value: reputation.overall,
      icon: Award,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+5'
    },
    {
      title: '粉丝数量',
      value: `${(playerTeam.fanBase / 10000).toFixed(1)}万`,
      icon: Heart,
      color: 'text-pink-600',
      bgColor: 'bg-pink-50',
      change: '+0.8万'
    },
    {
      title: '景点推广',
      value: attractions.filter(a => a.promotionLevel > 5).length,
      icon: MapPin,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+2'
    }
  ];

  const recentActivities = [
    { type: 'match', message: '上轮比赛2:1战胜苏州队', time: '3天前', icon: Trophy },
    { type: 'training', message: '林志强射门属性提升至88', time: '5天前', icon: TrendingUp },
    { type: 'cheerleader', message: '新增表演节目"龙城飞舞"', time: '1周前', icon: Heart },
    { type: 'tourism', message: '中华恐龙园推广等级提升', time: '1周前', icon: MapPin },
  ];

  return (
    <div className="space-y-6">
      {/* 欢迎标题 */}
      <div className="text-center py-8">
        <h1 className="text-4xl font-bold text-gradient mb-4">
          假如我是常州
        </h1>
        <p className="text-xl text-gray-600 mb-2">
          足球管理 × 文旅推广 × 城市营销
        </p>
        <p className="text-gray-500">
          带领常州队征战苏超，同时推广龙城文化魅力
        </p>
      </div>

      {/* 核心数据卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="card-hover p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 mt-1">
                    {stat.change} 本周
                  </p>
                </div>
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 下场比赛 */}
        <div className="lg:col-span-2">
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">下场比赛</h3>
              <Calendar className="w-5 h-5 text-gray-400" />
            </div>
            
            {nextMatch ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="team-badge">常</div>
                    <div>
                      <p className="font-semibold">{nextMatch.homeTeam.name}</p>
                      <p className="text-sm text-gray-600">主场</p>
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-lg font-bold text-gray-900">VS</p>
                    <p className="text-sm text-gray-600">
                      {nextMatch.date.toLocaleDateString('zh-CN')}
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div>
                      <p className="font-semibold text-right">{nextMatch.awayTeam.name}</p>
                      <p className="text-sm text-gray-600 text-right">客场</p>
                    </div>
                    <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-white font-bold">
                      {nextMatch.awayTeam.name.charAt(0)}
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-sm text-gray-600">比赛地点</p>
                    <p className="font-semibold">{nextMatch.venue}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">比赛轮次</p>
                    <p className="font-semibold">第{nextMatch.round}轮</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">预计观众</p>
                    <p className="font-semibold">{(playerTeam.capacity * 0.8 / 10000).toFixed(1)}万</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>暂无安排的比赛</p>
              </div>
            )}
          </div>
        </div>

        {/* 快速操作 */}
        <div className="space-y-6">
          {/* 球队状态 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">球队状态</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">平均状态</span>
                <span className="font-semibold">
                  {Math.floor(playerTeam.players.reduce((sum, p) => sum + p.form, 0) / playerTeam.players.length)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">平均体能</span>
                <span className="font-semibold">
                  {Math.floor(playerTeam.players.reduce((sum, p) => sum + p.fitness, 0) / playerTeam.players.length)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">平均士气</span>
                <span className="font-semibold">
                  {Math.floor(playerTeam.players.reduce((sum, p) => sum + p.morale, 0) / playerTeam.players.length)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">伤病球员</span>
                <span className="font-semibold text-red-600">
                  {playerTeam.players.filter(p => p.isInjured).length}
                </span>
              </div>
            </div>
          </div>

          {/* 啦啦队状态 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">啦啦队</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">成员数量</span>
                <span className="font-semibold">{cheerleaders.members.length}人</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">声望等级</span>
                <span className="font-semibold">{cheerleaders.reputation}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">粉丝参与度</span>
                <span className="font-semibold">{cheerleaders.fanEngagement}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">表演节目</span>
                <span className="font-semibold">{cheerleaders.performances.length}个</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 最近活动 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">最近活动</h3>
        <div className="space-y-4">
          {recentActivities.map((activity, index) => {
            const Icon = activity.icon;
            return (
              <div key={index} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="p-2 bg-white rounded-lg shadow-sm">
                  <Icon className="w-4 h-4 text-gray-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
