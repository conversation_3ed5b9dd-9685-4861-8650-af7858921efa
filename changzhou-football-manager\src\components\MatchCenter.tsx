import React, { useState } from 'react';
import { useGameStore } from '../store/gameStore';
import { 
  Play, 
  Trophy, 
  Calendar, 
  Users,
  Target,
  Clock,
  MapPin,
  TrendingUp
} from 'lucide-react';

const MatchCenter: React.FC = () => {
  const [isSimulating, setIsSimulating] = useState(false);
  const { 
    playerTeam, 
    allTeams, 
    simulateMatch, 
    getNextMatch,
    currentSeason 
  } = useGameStore();

  const nextMatch = getNextMatch();

  const handleSimulateMatch = async () => {
    if (!nextMatch) return;
    
    setIsSimulating(true);
    
    // 模拟比赛延迟
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const result = simulateMatch(nextMatch.awayTeam);
    setIsSimulating(false);
    
    // 这里可以显示比赛结果
    alert(`比赛结果: ${result.result?.homeScore} - ${result.result?.awayScore}`);
  };

  const recentMatches = [
    {
      opponent: '苏州队',
      result: 'win',
      score: '2-1',
      date: '2024-02-25',
      venue: '主场'
    },
    {
      opponent: '南京队',
      result: 'draw',
      score: '1-1',
      date: '2024-02-18',
      venue: '客场'
    },
    {
      opponent: '无锡队',
      result: 'loss',
      score: '0-2',
      date: '2024-02-11',
      venue: '主场'
    }
  ];

  const upcomingMatches = allTeams
    .filter(team => team.id !== playerTeam.id)
    .slice(0, 5)
    .map((team, index) => ({
      opponent: team.name,
      date: new Date(Date.now() + (index + 1) * 7 * 24 * 60 * 60 * 1000),
      venue: index % 2 === 0 ? '主场' : '客场',
      round: currentSeason.currentRound + index + 1
    }));

  const getResultColor = (result: string) => {
    switch (result) {
      case 'win': return 'text-green-600 bg-green-50';
      case 'draw': return 'text-yellow-600 bg-yellow-50';
      case 'loss': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getResultText = (result: string) => {
    switch (result) {
      case 'win': return '胜';
      case 'draw': return '平';
      case 'loss': return '负';
      default: return '-';
    }
  };

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">比赛中心</h2>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <span>当前轮次: 第{currentSeason.currentRound}轮</span>
          <span>总轮次: {currentSeason.totalRounds}轮</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 下场比赛 */}
        <div className="lg:col-span-2">
          <div className="card p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">下场比赛</h3>
              <Calendar className="w-5 h-5 text-gray-400" />
            </div>
            
            {nextMatch ? (
              <div className="space-y-6">
                {/* 比赛信息 */}
                <div className="bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="team-badge">常</div>
                      <div>
                        <p className="font-bold text-lg">{nextMatch.homeTeam.name}</p>
                        <p className="text-sm text-gray-600">主场作战</p>
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">VS</p>
                      <p className="text-sm text-gray-600">
                        {nextMatch.date.toLocaleDateString('zh-CN')}
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="font-bold text-lg">{nextMatch.awayTeam.name}</p>
                        <p className="text-sm text-gray-600">客场挑战</p>
                      </div>
                      <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-white font-bold">
                        {nextMatch.awayTeam.name.charAt(0)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-4 gap-4 text-center text-sm">
                    <div>
                      <p className="text-gray-600">比赛地点</p>
                      <p className="font-semibold">{nextMatch.venue}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">比赛轮次</p>
                      <p className="font-semibold">第{nextMatch.round}轮</p>
                    </div>
                    <div>
                      <p className="text-gray-600">预计观众</p>
                      <p className="font-semibold">{(playerTeam.capacity * 0.8 / 10000).toFixed(1)}万</p>
                    </div>
                    <div>
                      <p className="text-gray-600">天气</p>
                      <p className="font-semibold">晴朗</p>
                    </div>
                  </div>
                </div>

                {/* 比赛操作 */}
                <div className="flex justify-center">
                  <button
                    onClick={handleSimulateMatch}
                    disabled={isSimulating}
                    className={`btn-primary px-8 py-3 text-lg ${
                      isSimulating ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    {isSimulating ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>比赛进行中...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Play className="w-5 h-5" />
                        <span>开始比赛</span>
                      </div>
                    )}
                  </button>
                </div>

                {/* 球队对比 */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <h4 className="font-semibold mb-3">{nextMatch.homeTeam.name}</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>声望:</span>
                        <span className="font-semibold">{nextMatch.homeTeam.reputation}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>平均评分:</span>
                        <span className="font-semibold">
                          {Math.floor(nextMatch.homeTeam.players.reduce((sum, p) => sum + p.overall, 0) / nextMatch.homeTeam.players.length || 75)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>主场优势:</span>
                        <span className="font-semibold text-green-600">+5</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <h4 className="font-semibold mb-3">{nextMatch.awayTeam.name}</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>声望:</span>
                        <span className="font-semibold">{nextMatch.awayTeam.reputation}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>平均评分:</span>
                        <span className="font-semibold">
                          {Math.floor(nextMatch.awayTeam.players.reduce((sum, p) => sum + p.overall, 0) / nextMatch.awayTeam.players.length || 70)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>客场劣势:</span>
                        <span className="font-semibold text-red-600">-3</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <Calendar className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg">暂无安排的比赛</p>
                <p className="text-sm">请等待赛程安排</p>
              </div>
            )}
          </div>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 最近比赛结果 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">最近比赛</h3>
            <div className="space-y-3">
              {recentMatches.map((match, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{match.opponent}</p>
                    <p className="text-xs text-gray-500">{match.date}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">{match.score}</p>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getResultColor(match.result)}`}>
                      {getResultText(match.result)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 未来赛程 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">未来赛程</h3>
            <div className="space-y-3">
              {upcomingMatches.map((match, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{match.opponent}</p>
                    <p className="text-xs text-gray-500">第{match.round}轮</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm">{match.date.toLocaleDateString('zh-CN')}</p>
                    <p className="text-xs text-gray-500">{match.venue}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 球队状态 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">球队状态</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">整体士气</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full" 
                      style={{ width: `${Math.floor(playerTeam.players.reduce((sum, p) => sum + p.morale, 0) / playerTeam.players.length)}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-semibold">
                    {Math.floor(playerTeam.players.reduce((sum, p) => sum + p.morale, 0) / playerTeam.players.length)}
                  </span>
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">平均体能</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full" 
                      style={{ width: `${Math.floor(playerTeam.players.reduce((sum, p) => sum + p.fitness, 0) / playerTeam.players.length)}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-semibold">
                    {Math.floor(playerTeam.players.reduce((sum, p) => sum + p.fitness, 0) / playerTeam.players.length)}
                  </span>
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">伤病情况</span>
                <span className="text-sm font-semibold text-red-600">
                  {playerTeam.players.filter(p => p.isInjured).length} 人受伤
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MatchCenter;
