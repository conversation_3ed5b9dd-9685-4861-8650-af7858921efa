// 球员位置枚举
export enum Position {
  GK = 'GK',   // 门将
  CB = 'CB',   // 中后卫
  LB = 'LB',   // 左后卫
  RB = 'RB',   // 右后卫
  CDM = 'CDM', // 后腰
  CM = 'CM',   // 中场
  CAM = 'CAM', // 前腰
  LM = 'LM',   // 左中场
  RM = 'RM',   // 右中场
  LW = 'LW',   // 左边锋
  RW = 'RW',   // 右边锋
  ST = 'ST',   // 前锋
  CF = 'CF'    // 中锋
}

// 球员属性
export interface PlayerAttributes {
  pace: number;        // 速度 (0-100)
  shooting: number;    // 射门 (0-100)
  passing: number;     // 传球 (0-100)
  dribbling: number;   // 盘带 (0-100)
  defending: number;   // 防守 (0-100)
  physical: number;    // 身体 (0-100)
}

// 球员数据
export interface Player {
  id: string;
  name: string;
  age: number;
  position: Position;
  primaryPosition: Position;
  secondaryPositions: Position[];
  attributes: PlayerAttributes;
  overall: number;     // 总评 (0-100)
  potential: number;   // 潜力 (0-100)
  value: number;       // 身价 (万元)
  salary: number;      // 薪水 (万元/年)
  contract: {
    startYear: number;
    endYear: number;
  };
  nationality: string;
  height: number;      // 身高 (cm)
  weight: number;      // 体重 (kg)
  foot: 'left' | 'right' | 'both';
  form: number;        // 状态 (0-100)
  fitness: number;     // 体能 (0-100)
  morale: number;      // 士气 (0-100)
  isInjured: boolean;
  injuryDays?: number;
  isYouthPlayer: boolean;
  avatar?: string;
}

// 阵型定义
export interface Formation {
  id: string;
  name: string;
  positions: {
    position: Position;
    x: number;  // 相对位置 (0-100)
    y: number;  // 相对位置 (0-100)
  }[];
  description: string;
}

// 战术设置
export interface Tactics {
  formation: Formation;
  mentality: 'defensive' | 'balanced' | 'attacking';
  tempo: 'slow' | 'normal' | 'fast';
  width: 'narrow' | 'normal' | 'wide';
  passingStyle: 'short' | 'mixed' | 'long';
  defensiveLine: 'deep' | 'normal' | 'high';
  pressing: 'low' | 'medium' | 'high';
}

// 球队数据
export interface Team {
  id: string;
  name: string;
  city: string;
  founded: number;
  stadium: string;
  capacity: number;
  colors: {
    primary: string;
    secondary: string;
  };
  logo?: string;
  players: Player[];
  tactics: Tactics;
  budget: number;      // 预算 (万元)
  reputation: number;  // 声望 (0-100)
  fanBase: number;     // 粉丝数量
  facilities: {
    training: number;  // 训练设施等级 (1-10)
    youth: number;     // 青训设施等级 (1-10)
    medical: number;   // 医疗设施等级 (1-10)
    stadium: number;   // 球场设施等级 (1-10)
  };
}

// 比赛结果
export interface MatchResult {
  homeScore: number;
  awayScore: number;
  result: 'win' | 'draw' | 'loss'; // 从主队角度
}

// 比赛数据
export interface Match {
  id: string;
  homeTeam: Team;
  awayTeam: Team;
  date: Date;
  venue: string;
  competition: string;
  round: number;
  result?: MatchResult;
  attendance?: number;
  weather?: 'sunny' | 'cloudy' | 'rainy' | 'snowy';
  events: MatchEvent[];
}

// 比赛事件
export interface MatchEvent {
  minute: number;
  type: 'goal' | 'yellow_card' | 'red_card' | 'substitution' | 'penalty';
  player: Player;
  team: 'home' | 'away';
  description: string;
}

// 联赛积分榜
export interface LeagueTable {
  position: number;
  team: Team;
  played: number;
  won: number;
  drawn: number;
  lost: number;
  goalsFor: number;
  goalsAgainst: number;
  goalDifference: number;
  points: number;
  form: ('W' | 'D' | 'L')[];
}

// 赛季数据
export interface Season {
  year: number;
  currentRound: number;
  totalRounds: number;
  matches: Match[];
  table: LeagueTable[];
  topScorers: {
    player: Player;
    goals: number;
    team: Team;
  }[];
}

// 啦啦队成员
export interface CheerleaderMember {
  id: string;
  name: string;
  age: number;
  position: 'captain' | 'dancer' | 'singer' | 'performer';
  skills: {
    dance: number;    // 舞蹈技能 (0-100)
    singing: number;  // 歌唱技能 (0-100)
    charisma: number; // 魅力 (0-100)
    energy: number;   // 活力 (0-100)
  };
  salary: number;
  contract: {
    startYear: number;
    endYear: number;
  };
  avatar?: string;
}

// 表演节目
export interface Performance {
  id: string;
  name: string;
  type: 'dance' | 'song' | 'cheer' | 'acrobatics';
  duration: number; // 分钟
  difficulty: number; // 难度 (1-10)
  popularity: number; // 受欢迎程度 (0-100)
  cost: number; // 成本
  requirements: {
    minMembers: number;
    requiredSkills: {
      dance?: number;
      singing?: number;
      charisma?: number;
      energy?: number;
    };
  };
  description: string;
}

// 啦啦队
export interface Cheerleaders {
  members: CheerleaderMember[];
  performances: Performance[];
  budget: number;
  reputation: number;
  fanEngagement: number; // 粉丝参与度 (0-100)
}

// 文旅景点
export interface TouristAttraction {
  id: string;
  name: string;
  type: 'historical' | 'cultural' | 'natural' | 'modern';
  description: string;
  popularity: number; // 受欢迎程度 (0-100)
  visitCost: number;  // 参观费用
  promotionLevel: number; // 推广等级 (0-10)
  image?: string;
}

// 特色商品
export interface Merchandise {
  id: string;
  name: string;
  type: 'jersey' | 'scarf' | 'food' | 'souvenir' | 'cultural';
  price: number;
  cost: number;
  popularity: number;
  stock: number;
  description: string;
  isChangzhouSpecial: boolean; // 是否为常州特色
  image?: string;
}

// 文旅推广活动
export interface TourismPromotion {
  id: string;
  name: string;
  type: 'match_day' | 'cultural_festival' | 'food_fair' | 'scenic_tour';
  cost: number;
  duration: number; // 天数
  expectedVisitors: number;
  attractions: TouristAttraction[];
  merchandise: Merchandise[];
  description: string;
  startDate: Date;
  endDate: Date;
}

// 游戏状态
export interface GameState {
  currentSeason: Season;
  playerTeam: Team;
  allTeams: Team[];
  cheerleaders: Cheerleaders;
  attractions: TouristAttraction[];
  merchandise: Merchandise[];
  activePromotions: TourismPromotion[];
  gameDate: Date;
  difficulty: 'easy' | 'normal' | 'hard';
  budget: {
    football: number;    // 足球预算
    cheerleaders: number; // 啦啦队预算
    tourism: number;     // 文旅预算
    total: number;       // 总预算
  };
  reputation: {
    football: number;    // 足球声望
    cultural: number;    // 文化声望
    tourism: number;     // 旅游声望
    overall: number;     // 总体声望
  };
}
