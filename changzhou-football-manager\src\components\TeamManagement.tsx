import React, { useState } from 'react';
import { useGameStore } from '../store/gameStore';
import { formations } from '../data/changzhouTeam';
import { Position } from '../types/game';
import { 
  Users, 
  Settings, 
  TrendingUp, 
  Activity,
  Target,
  Zap
} from 'lucide-react';

type TabType = 'formation' | 'players' | 'tactics' | 'training';

const TeamManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('formation');
  const { 
    playerTeam, 
    updateTactics, 
    setFormation, 
    updatePlayerPosition,
    trainPlayer 
  } = useGameStore();

  const tabs = [
    { id: 'formation', label: '阵型设置', icon: Users },
    { id: 'players', label: '球员管理', icon: Target },
    { id: 'tactics', label: '战术设置', icon: Settings },
    { id: 'training', label: '训练管理', icon: TrendingUp },
  ] as const;

  const getPositionColor = (position: Position) => {
    if (position === Position.GK) return 'bg-yellow-500';
    if ([Position.CB, Position.LB, Position.RB].includes(position)) return 'bg-blue-500';
    if ([Position.CDM, Position.CM, Position.CAM, Position.LM, Position.RM].includes(position)) return 'bg-green-500';
    return 'bg-red-500';
  };

  const renderFormationView = () => (
    <div className="space-y-6">
      {/* 阵型选择 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold mb-4">选择阵型</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {formations.map((formation) => (
            <button
              key={formation.id}
              onClick={() => setFormation(formation)}
              className={`p-4 rounded-lg border-2 transition-all ${
                playerTeam.tactics.formation.id === formation.id
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <h4 className="font-semibold mb-2">{formation.name}</h4>
              <p className="text-sm text-gray-600">{formation.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* 阵型预览 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold mb-4">阵型预览 - {playerTeam.tactics.formation.name}</h3>
        <div className="formation-grid" style={{ height: '400px', gridTemplateRows: 'repeat(10, 1fr)' }}>
          {playerTeam.tactics.formation.positions.map((pos, index) => {
            const player = playerTeam.players.find(p => p.position === pos.position);
            return (
              <div
                key={index}
                className="player-position"
                style={{
                  gridColumn: Math.floor(pos.x / 10) + 1,
                  gridRow: Math.floor(pos.y / 10) + 1,
                }}
              >
                <div className="text-center">
                  <div className={`w-3 h-3 rounded-full mx-auto mb-1 ${getPositionColor(pos.position)}`}></div>
                  <div className="text-xs font-bold">{pos.position}</div>
                  {player && (
                    <div className="text-xs text-gray-600 mt-1">{player.name}</div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );

  const renderPlayersView = () => (
    <div className="space-y-6">
      <div className="card p-6">
        <h3 className="text-lg font-semibold mb-4">球员名单</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4">姓名</th>
                <th className="text-left py-3 px-4">位置</th>
                <th className="text-left py-3 px-4">年龄</th>
                <th className="text-left py-3 px-4">总评</th>
                <th className="text-left py-3 px-4">状态</th>
                <th className="text-left py-3 px-4">体能</th>
                <th className="text-left py-3 px-4">士气</th>
                <th className="text-left py-3 px-4">身价</th>
              </tr>
            </thead>
            <tbody>
              {playerTeam.players.map((player) => (
                <tr key={player.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4">
                    <div>
                      <div className="font-medium">{player.name}</div>
                      <div className="text-sm text-gray-500">{player.nationality}</div>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium text-white ${getPositionColor(player.position)}`}>
                      {player.position}
                    </span>
                  </td>
                  <td className="py-3 px-4">{player.age}</td>
                  <td className="py-3 px-4">
                    <span className="font-semibold">{player.overall}</span>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center">
                      <div className={`w-2 h-2 rounded-full mr-2 ${
                        player.form >= 80 ? 'bg-green-500' : 
                        player.form >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                      {player.form}
                    </div>
                  </td>
                  <td className="py-3 px-4">{player.fitness}</td>
                  <td className="py-3 px-4">{player.morale}</td>
                  <td className="py-3 px-4">{player.value}万</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderTacticsView = () => (
    <div className="space-y-6">
      <div className="card p-6">
        <h3 className="text-lg font-semibold mb-4">战术设置</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">心态</label>
            <select 
              value={playerTeam.tactics.mentality}
              onChange={(e) => updateTactics({ mentality: e.target.value as any })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="defensive">防守型</option>
              <option value="balanced">平衡型</option>
              <option value="attacking">攻击型</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">节奏</label>
            <select 
              value={playerTeam.tactics.tempo}
              onChange={(e) => updateTactics({ tempo: e.target.value as any })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="slow">慢节奏</option>
              <option value="normal">正常节奏</option>
              <option value="fast">快节奏</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">宽度</label>
            <select 
              value={playerTeam.tactics.width}
              onChange={(e) => updateTactics({ width: e.target.value as any })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="narrow">收缩</option>
              <option value="normal">正常</option>
              <option value="wide">拉开</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">传球风格</label>
            <select 
              value={playerTeam.tactics.passingStyle}
              onChange={(e) => updateTactics({ passingStyle: e.target.value as any })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="short">短传</option>
              <option value="mixed">混合</option>
              <option value="long">长传</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">防线高度</label>
            <select 
              value={playerTeam.tactics.defensiveLine}
              onChange={(e) => updateTactics({ defensiveLine: e.target.value as any })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="deep">深度防守</option>
              <option value="normal">正常</option>
              <option value="high">高位防线</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">逼抢强度</label>
            <select 
              value={playerTeam.tactics.pressing}
              onChange={(e) => updateTactics({ pressing: e.target.value as any })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="low">低强度</option>
              <option value="medium">中等强度</option>
              <option value="high">高强度</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTrainingView = () => (
    <div className="space-y-6">
      <div className="card p-6">
        <h3 className="text-lg font-semibold mb-4">训练管理</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {playerTeam.players.slice(0, 6).map((player) => (
            <div key={player.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-semibold">{player.name}</h4>
                  <p className="text-sm text-gray-600">{player.position} | 总评 {player.overall}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">潜力</p>
                  <p className="font-semibold">{player.potential}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                {Object.entries(player.attributes).map(([attr, value]) => (
                  <button
                    key={attr}
                    onClick={() => trainPlayer(player.id, attr as keyof typeof player.attributes)}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors"
                  >
                    <span className="text-sm capitalize">{attr}</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold">{value}</span>
                      <Zap className="w-3 h-3 text-yellow-500" />
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'formation':
        return renderFormationView();
      case 'players':
        return renderPlayersView();
      case 'tactics':
        return renderTacticsView();
      case 'training':
        return renderTrainingView();
      default:
        return renderFormationView();
    }
  };

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">球队管理</h2>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <span>球员总数: {playerTeam.players.length}</span>
          <span>平均年龄: {Math.floor(playerTeam.players.reduce((sum, p) => sum + p.age, 0) / playerTeam.players.length)}</span>
          <span>平均总评: {Math.floor(playerTeam.players.reduce((sum, p) => sum + p.overall, 0) / playerTeam.players.length)}</span>
        </div>
      </div>

      {/* 标签导航 */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* 内容区域 */}
      {renderContent()}
    </div>
  );
};

export default TeamManagement;
