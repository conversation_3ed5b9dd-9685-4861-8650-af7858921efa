import { create } from 'zustand';
import { GameState, Team, Player, Match, Formation, Tactics } from '../types/game';
import { changzhouTeam } from '../data/changzhouTeam';
import { changzhouAttractions, changzhouMerchandise, changzhouCheerleaders, cheerleaderPerformances } from '../data/changzhouTourism';

interface GameStore extends GameState {
  // 游戏控制
  initializeGame: () => void;
  setDifficulty: (difficulty: 'easy' | 'normal' | 'hard') => void;
  advanceDate: (days: number) => void;
  
  // 球队管理
  updateTactics: (tactics: Partial<Tactics>) => void;
  setFormation: (formation: Formation) => void;
  updatePlayerPosition: (playerId: string, position: string) => void;
  trainPlayer: (playerId: string, attribute: keyof Player['attributes']) => void;
  
  // 比赛相关
  simulateMatch: (opponent: Team) => Match;
  getNextMatch: () => Match | null;
  
  // 财务管理
  spendBudget: (category: 'football' | 'cheerleaders' | 'tourism', amount: number) => boolean;
  earnRevenue: (category: 'football' | 'cheerleaders' | 'tourism', amount: number) => void;
  
  // 啦啦队管理
  hireCheerleader: (cheerleader: any) => boolean;
  schedulePerformance: (performanceId: string, matchId: string) => boolean;
  
  // 文旅推广
  promoteAttraction: (attractionId: string, investment: number) => void;
  launchMerchandise: (merchandiseId: string, quantity: number) => boolean;
  
  // 声望系统
  updateReputation: (category: 'football' | 'cultural' | 'tourism', change: number) => void;
}

// 创建其他球队数据
const createOpponentTeams = (): Team[] => {
  const cities = [
    { name: '南京队', city: '南京', colors: { primary: '#8B5CF6', secondary: '#F59E0B' } },
    { name: '苏州队', city: '苏州', colors: { primary: '#EF4444', secondary: '#FFFFFF' } },
    { name: '无锡队', city: '无锡', colors: { primary: '#10B981', secondary: '#1F2937' } },
    { name: '徐州队', city: '徐州', colors: { primary: '#F97316', secondary: '#1E40AF' } },
    { name: '扬州队', city: '扬州', colors: { primary: '#84CC16', secondary: '#DC2626' } },
    { name: '盐城队', city: '盐城', colors: { primary: '#06B6D4', secondary: '#7C3AED' } },
    { name: '淮安队', city: '淮安', colors: { primary: '#EC4899', secondary: '#059669' } },
    { name: '连云港队', city: '连云港', colors: { primary: '#6366F1', secondary: '#F59E0B' } },
    { name: '南通队', city: '南通', colors: { primary: '#8B5CF6', secondary: '#EF4444' } },
    { name: '镇江队', city: '镇江', colors: { primary: '#10B981', secondary: '#F97316' } },
    { name: '泰州队', city: '泰州', colors: { primary: '#F59E0B', secondary: '#8B5CF6' } },
    { name: '宿迁队', city: '宿迁', colors: { primary: '#EF4444', secondary: '#10B981' } }
  ];

  return cities.map((city, index) => ({
    id: `team-${index + 1}`,
    name: city.name,
    city: city.city,
    founded: 2020,
    stadium: `${city.city}体育中心`,
    capacity: 25000 + Math.random() * 10000,
    colors: city.colors,
    players: [], // 简化处理，实际游戏中会生成完整球员数据
    tactics: {
      formation: changzhouTeam.tactics.formation,
      mentality: 'balanced',
      tempo: 'normal',
      width: 'normal',
      passingStyle: 'mixed',
      defensiveLine: 'normal',
      pressing: 'medium'
    },
    budget: 1500 + Math.random() * 1000,
    reputation: 60 + Math.random() * 30,
    fanBase: 80000 + Math.random() * 100000,
    facilities: {
      training: Math.floor(5 + Math.random() * 4),
      youth: Math.floor(4 + Math.random() * 4),
      medical: Math.floor(5 + Math.random() * 4),
      stadium: Math.floor(6 + Math.random() * 3)
    }
  }));
};

// 简单的比赛模拟算法
const simulateMatchResult = (homeTeam: Team, awayTeam: Team) => {
  const homeStrength = homeTeam.reputation + (homeTeam.players.length > 0 ? 
    homeTeam.players.reduce((sum, p) => sum + p.overall, 0) / homeTeam.players.length : 70);
  const awayStrength = awayTeam.reputation + (awayTeam.players.length > 0 ? 
    awayTeam.players.reduce((sum, p) => sum + p.overall, 0) / awayTeam.players.length : 70);
  
  // 主场优势
  const adjustedHomeStrength = homeStrength + 5;
  
  // 随机因素
  const homeRandom = Math.random() * 20 - 10;
  const awayRandom = Math.random() * 20 - 10;
  
  const finalHomeStrength = adjustedHomeStrength + homeRandom;
  const finalAwayStrength = awayStrength + awayRandom;
  
  // 计算进球数
  const homeGoals = Math.max(0, Math.floor((finalHomeStrength - 50) / 15) + Math.floor(Math.random() * 3));
  const awayGoals = Math.max(0, Math.floor((finalAwayStrength - 50) / 15) + Math.floor(Math.random() * 3));
  
  let result: 'win' | 'draw' | 'loss';
  if (homeGoals > awayGoals) result = 'win';
  else if (homeGoals < awayGoals) result = 'loss';
  else result = 'draw';
  
  return {
    homeScore: homeGoals,
    awayScore: awayGoals,
    result
  };
};

export const useGameStore = create<GameStore>((set, get) => ({
  // 初始状态
  currentSeason: {
    year: 2024,
    currentRound: 1,
    totalRounds: 22,
    matches: [],
    table: [],
    topScorers: []
  },
  playerTeam: changzhouTeam,
  allTeams: [],
  cheerleaders: {
    members: changzhouCheerleaders,
    performances: cheerleaderPerformances,
    budget: 50,
    reputation: 75,
    fanEngagement: 80
  },
  attractions: changzhouAttractions,
  merchandise: changzhouMerchandise,
  activePromotions: [],
  gameDate: new Date('2024-03-01'),
  difficulty: 'normal',
  budget: {
    football: 1500,
    cheerleaders: 50,
    tourism: 200,
    total: 1750
  },
  reputation: {
    football: 75,
    cultural: 70,
    tourism: 65,
    overall: 70
  },

  // 游戏控制方法
  initializeGame: () => {
    const opponentTeams = createOpponentTeams();
    set({
      allTeams: [changzhouTeam, ...opponentTeams],
      gameDate: new Date('2024-03-01')
    });
  },

  setDifficulty: (difficulty) => {
    set({ difficulty });
  },

  advanceDate: (days) => {
    const state = get();
    const newDate = new Date(state.gameDate);
    newDate.setDate(newDate.getDate() + days);
    set({ gameDate: newDate });
  },

  // 球队管理方法
  updateTactics: (newTactics) => {
    set((state) => ({
      playerTeam: {
        ...state.playerTeam,
        tactics: { ...state.playerTeam.tactics, ...newTactics }
      }
    }));
  },

  setFormation: (formation) => {
    set((state) => ({
      playerTeam: {
        ...state.playerTeam,
        tactics: { ...state.playerTeam.tactics, formation }
      }
    }));
  },

  updatePlayerPosition: (playerId, position) => {
    set((state) => ({
      playerTeam: {
        ...state.playerTeam,
        players: state.playerTeam.players.map(player =>
          player.id === playerId 
            ? { ...player, position: position as any }
            : player
        )
      }
    }));
  },

  trainPlayer: (playerId, attribute) => {
    set((state) => ({
      playerTeam: {
        ...state.playerTeam,
        players: state.playerTeam.players.map(player =>
          player.id === playerId 
            ? {
                ...player,
                attributes: {
                  ...player.attributes,
                  [attribute]: Math.min(100, player.attributes[attribute] + 1)
                }
              }
            : player
        )
      }
    }));
  },

  // 比赛相关方法
  simulateMatch: (opponent) => {
    const state = get();
    const result = simulateMatchResult(state.playerTeam, opponent);
    
    const match: Match = {
      id: `match-${Date.now()}`,
      homeTeam: state.playerTeam,
      awayTeam: opponent,
      date: new Date(state.gameDate),
      venue: state.playerTeam.stadium,
      competition: '苏超联赛',
      round: state.currentSeason.currentRound,
      result,
      attendance: Math.floor(state.playerTeam.capacity * (0.6 + Math.random() * 0.4)),
      weather: ['sunny', 'cloudy', 'rainy'][Math.floor(Math.random() * 3)] as any,
      events: []
    };

    return match;
  },

  getNextMatch: () => {
    const state = get();
    if (state.allTeams.length === 0) return null;
    
    const opponents = state.allTeams.filter(team => team.id !== state.playerTeam.id);
    const nextOpponent = opponents[Math.floor(Math.random() * opponents.length)];
    
    return {
      id: `next-match-${Date.now()}`,
      homeTeam: state.playerTeam,
      awayTeam: nextOpponent,
      date: new Date(state.gameDate.getTime() + 7 * 24 * 60 * 60 * 1000), // 一周后
      venue: state.playerTeam.stadium,
      competition: '苏超联赛',
      round: state.currentSeason.currentRound,
      events: []
    };
  },

  // 财务管理方法
  spendBudget: (category, amount) => {
    const state = get();
    if (state.budget[category] >= amount) {
      set((state) => ({
        budget: {
          ...state.budget,
          [category]: state.budget[category] - amount,
          total: state.budget.total - amount
        }
      }));
      return true;
    }
    return false;
  },

  earnRevenue: (category, amount) => {
    set((state) => ({
      budget: {
        ...state.budget,
        [category]: state.budget[category] + amount,
        total: state.budget.total + amount
      }
    }));
  },

  // 啦啦队管理方法
  hireCheerleader: (cheerleader) => {
    const state = get();
    const cost = cheerleader.salary * 12; // 年薪
    if (state.budget.cheerleaders >= cost) {
      set((state) => ({
        cheerleaders: {
          ...state.cheerleaders,
          members: [...state.cheerleaders.members, cheerleader]
        },
        budget: {
          ...state.budget,
          cheerleaders: state.budget.cheerleaders - cost,
          total: state.budget.total - cost
        }
      }));
      return true;
    }
    return false;
  },

  schedulePerformance: (performanceId, matchId) => {
    // 实现表演安排逻辑
    return true;
  },

  // 文旅推广方法
  promoteAttraction: (attractionId, investment) => {
    const state = get();
    if (state.budget.tourism >= investment) {
      set((state) => ({
        attractions: state.attractions.map(attraction =>
          attraction.id === attractionId
            ? {
                ...attraction,
                promotionLevel: Math.min(10, attraction.promotionLevel + Math.floor(investment / 10)),
                popularity: Math.min(100, attraction.popularity + Math.floor(investment / 20))
              }
            : attraction
        ),
        budget: {
          ...state.budget,
          tourism: state.budget.tourism - investment,
          total: state.budget.total - investment
        }
      }));
    }
  },

  launchMerchandise: (merchandiseId, quantity) => {
    const state = get();
    const item = state.merchandise.find(m => m.id === merchandiseId);
    if (item) {
      const cost = item.cost * quantity;
      if (state.budget.tourism >= cost) {
        set((state) => ({
          merchandise: state.merchandise.map(merch =>
            merch.id === merchandiseId
              ? { ...merch, stock: merch.stock + quantity }
              : merch
          ),
          budget: {
            ...state.budget,
            tourism: state.budget.tourism - cost,
            total: state.budget.total - cost
          }
        }));
        return true;
      }
    }
    return false;
  },

  // 声望系统方法
  updateReputation: (category, change) => {
    set((state) => {
      const newReputation = {
        ...state.reputation,
        [category]: Math.max(0, Math.min(100, state.reputation[category] + change))
      };
      
      // 计算总体声望
      newReputation.overall = Math.floor(
        (newReputation.football + newReputation.cultural + newReputation.tourism) / 3
      );
      
      return { reputation: newReputation };
    });
  }
}));
