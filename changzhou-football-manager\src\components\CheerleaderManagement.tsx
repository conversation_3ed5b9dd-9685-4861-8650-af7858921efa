import React, { useState } from 'react';
import { useGameStore } from '../store/gameStore';
import { 
  Heart, 
  Music, 
  Users, 
  Star,
  Plus,
  Play,
  Award,
  TrendingUp
} from 'lucide-react';

type TabType = 'members' | 'performances' | 'schedule' | 'stats';

const CheerleaderManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('members');
  const { 
    cheerleaders, 
    budget,
    hireCheerleader,
    schedulePerformance 
  } = useGameStore();

  const tabs = [
    { id: 'members', label: '成员管理', icon: Users },
    { id: 'performances', label: '表演节目', icon: Music },
    { id: 'schedule', label: '演出安排', icon: Play },
    { id: 'stats', label: '数据统计', icon: TrendingUp },
  ] as const;

  const getSkillColor = (skill: number) => {
    if (skill >= 90) return 'text-green-600 bg-green-50';
    if (skill >= 75) return 'text-blue-600 bg-blue-50';
    if (skill >= 60) return 'text-yellow-600 bg-yellow-50';
    return 'text-gray-600 bg-gray-50';
  };

  const getPositionIcon = (position: string) => {
    switch (position) {
      case 'captain': return '👑';
      case 'dancer': return '💃';
      case 'singer': return '🎤';
      case 'performer': return '🎭';
      default: return '⭐';
    }
  };

  const renderMembersView = () => (
    <div className="space-y-6">
      {/* 成员概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card p-4 text-center">
          <Users className="w-8 h-8 text-primary-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">{cheerleaders.members.length}</p>
          <p className="text-sm text-gray-600">总成员数</p>
        </div>
        <div className="card p-4 text-center">
          <Star className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">{cheerleaders.reputation}</p>
          <p className="text-sm text-gray-600">声望等级</p>
        </div>
        <div className="card p-4 text-center">
          <Heart className="w-8 h-8 text-pink-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">{cheerleaders.fanEngagement}%</p>
          <p className="text-sm text-gray-600">粉丝参与度</p>
        </div>
        <div className="card p-4 text-center">
          <Award className="w-8 h-8 text-purple-600 mx-auto mb-2" />
          <p className="text-2xl font-bold">{cheerleaders.budget}万</p>
          <p className="text-sm text-gray-600">可用预算</p>
        </div>
      </div>

      {/* 成员列表 */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">啦啦队成员</h3>
          <button className="btn-primary">
            <Plus className="w-4 h-4 mr-2" />
            招募新成员
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {cheerleaders.members.map((member) => (
            <div key={member.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h4 className="font-semibold flex items-center">
                    <span className="mr-2">{getPositionIcon(member.position)}</span>
                    {member.name}
                  </h4>
                  <p className="text-sm text-gray-600">{member.age}岁 | {member.position}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">年薪</p>
                  <p className="font-semibold">{member.salary}万</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">舞蹈</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getSkillColor(member.skills.dance)}`}>
                    {member.skills.dance}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">歌唱</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getSkillColor(member.skills.singing)}`}>
                    {member.skills.singing}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">魅力</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getSkillColor(member.skills.charisma)}`}>
                    {member.skills.charisma}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">活力</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getSkillColor(member.skills.energy)}`}>
                    {member.skills.energy}
                  </span>
                </div>
              </div>
              
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-500">
                  合同: {member.contract.startYear} - {member.contract.endYear}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderPerformancesView = () => (
    <div className="space-y-6">
      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">表演节目库</h3>
          <button className="btn-primary">
            <Plus className="w-4 h-4 mr-2" />
            创建新节目
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {cheerleaders.performances.map((performance) => (
            <div key={performance.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h4 className="font-semibold">{performance.name}</h4>
                  <p className="text-sm text-gray-600 capitalize">{performance.type} | {performance.duration}分钟</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">受欢迎度</p>
                  <p className="font-semibold text-primary-600">{performance.popularity}</p>
                </div>
              </div>
              
              <p className="text-sm text-gray-700 mb-3">{performance.description}</p>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">难度等级</p>
                  <div className="flex items-center">
                    {Array.from({ length: 10 }, (_, i) => (
                      <Star 
                        key={i} 
                        className={`w-3 h-3 ${i < performance.difficulty ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                      />
                    ))}
                  </div>
                </div>
                <div>
                  <p className="text-gray-600">成本</p>
                  <p className="font-semibold">{performance.cost}元</p>
                </div>
              </div>
              
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-600">
                  需要: {performance.requirements.minMembers}人以上
                </p>
                <div className="flex flex-wrap gap-1 mt-1">
                  {Object.entries(performance.requirements.requiredSkills).map(([skill, level]) => (
                    <span key={skill} className="px-2 py-1 bg-gray-100 rounded text-xs">
                      {skill}: {level}+
                    </span>
                  ))}
                </div>
              </div>
              
              <button className="w-full mt-3 btn-secondary text-sm">
                <Play className="w-3 h-3 mr-1" />
                安排表演
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderScheduleView = () => (
    <div className="space-y-6">
      <div className="card p-6">
        <h3 className="text-lg font-semibold mb-4">演出安排</h3>
        <div className="text-center py-12 text-gray-500">
          <Music className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg">暂无安排的演出</p>
          <p className="text-sm">在比赛中心安排比赛时可以选择表演节目</p>
        </div>
      </div>
    </div>
  );

  const renderStatsView = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card p-6">
          <h4 className="font-semibold mb-4">技能分布</h4>
          <div className="space-y-3">
            {['dance', 'singing', 'charisma', 'energy'].map((skill) => {
              const avgSkill = Math.floor(
                cheerleaders.members.reduce((sum, m) => sum + m.skills[skill as keyof typeof m.skills], 0) / 
                cheerleaders.members.length
              );
              return (
                <div key={skill} className="flex justify-between items-center">
                  <span className="text-sm capitalize">{skill}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full" 
                        style={{ width: `${avgSkill}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-semibold">{avgSkill}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        
        <div className="card p-6">
          <h4 className="font-semibold mb-4">成员构成</h4>
          <div className="space-y-3">
            {['captain', 'dancer', 'singer', 'performer'].map((position) => {
              const count = cheerleaders.members.filter(m => m.position === position).length;
              return (
                <div key={position} className="flex justify-between items-center">
                  <span className="text-sm capitalize flex items-center">
                    <span className="mr-2">{getPositionIcon(position)}</span>
                    {position}
                  </span>
                  <span className="font-semibold">{count}人</span>
                </div>
              );
            })}
          </div>
        </div>
        
        <div className="card p-6">
          <h4 className="font-semibold mb-4">财务状况</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm">可用预算</span>
              <span className="font-semibold text-green-600">{cheerleaders.budget}万</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">年度支出</span>
              <span className="font-semibold text-red-600">
                {cheerleaders.members.reduce((sum, m) => sum + m.salary, 0)}万
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">平均薪资</span>
              <span className="font-semibold">
                {(cheerleaders.members.reduce((sum, m) => sum + m.salary, 0) / cheerleaders.members.length).toFixed(1)}万
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'members':
        return renderMembersView();
      case 'performances':
        return renderPerformancesView();
      case 'schedule':
        return renderScheduleView();
      case 'stats':
        return renderStatsView();
      default:
        return renderMembersView();
    }
  };

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">啦啦队管理</h2>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <span>成员: {cheerleaders.members.length}人</span>
          <span>声望: {cheerleaders.reputation}</span>
          <span>预算: {cheerleaders.budget}万</span>
        </div>
      </div>

      {/* 标签导航 */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* 内容区域 */}
      {renderContent()}
    </div>
  );
};

export default CheerleaderManagement;
