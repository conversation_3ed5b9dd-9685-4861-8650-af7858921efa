import React, { useState, useEffect } from 'react';

interface Dialog {
  id: string;
  character: string;
  avatar: string;
  text: string;
  options?: {
    text: string;
    response: string;
    effect?: string;
  }[];
}

// 搞笑对话库
const dialogDatabase: Dialog[] = [
  {
    id: 'coach_intro',
    character: '教练老王',
    avatar: '👨‍🏫',
    text: '欢迎来到常州队！我是教练老王，执教经验丰富...主要是输球经验比较丰富。',
    options: [
      {
        text: '我们的目标是什么？',
        response: '目标？能不垫底就谢天谢地了！不过我们有个小目标：至少要比隔壁幼儿园足球队强一点。',
        effect: '士气+1'
      },
      {
        text: '球队有什么特色？',
        response: '我们的特色就是...呃...很有特色！比如我们的球员跑得特别慢，但是摔倒的姿势特别优美。',
        effect: '幽默+2'
      }
    ]
  },
  {
    id: 'player_complaint',
    character: '前锋小李',
    avatar: '⚽',
    text: '老板，我觉得我们的训练强度太大了！每天要跑5公里，我的小短腿快断了！',
    options: [
      {
        text: '那就减少到3公里',
        response: '太好了！不过...能不能再减少到1公里？我保证会很努力地走完的！',
        effect: '球员满意度+1，体能-1'
      },
      {
        text: '不行，必须坚持！',
        response: '呜呜呜...我要回家找妈妈！不过为了球队，我会坚持的...大概吧。',
        effect: '球员满意度-1，体能+1'
      }
    ]
  },
  {
    id: 'cheerleader_captain',
    character: '啦啦队队长小美',
    avatar: '💃',
    text: '老板，我们啦啦队想要新的服装！现在这套已经洗了100遍，颜色都快看不出来了。',
    options: [
      {
        text: '好的，给你们买新的',
        response: '太棒了！我们会跳得更卖力的！虽然观众可能还是在看球员...算了，至少我们自己开心！',
        effect: '预算-50万，啦啦队士气+3'
      },
      {
        text: '再坚持一下吧',
        response: '好吧...不过如果服装在比赛中散架了，可别怪我们哦！到时候可能会变成脱衣舞表演。',
        effect: '啦啦队士气-1，风险+1'
      }
    ]
  },
  {
    id: 'fan_feedback',
    character: '铁杆球迷老张',
    avatar: '🧓',
    text: '我支持常州队20年了！虽然我们总是输，但是我相信总有一天会赢的...大概吧？',
    options: [
      {
        text: '我们一定会努力的！',
        response: '好！我再支持你们20年！反正我也没别的爱好，看你们输球已经成习惯了。',
        effect: '球迷忠诚度+2'
      },
      {
        text: '要不你换个队支持？',
        response: '不行！我是常州人，死也要支持常州队！虽然确实很想死...',
        effect: '球迷忠诚度+1，幽默+1'
      }
    ]
  },
  {
    id: 'sponsor_meeting',
    character: '赞助商老板',
    avatar: '💼',
    text: '我考虑赞助你们球队，但是...你们的成绩实在是...怎么说呢，很有"特色"。',
    options: [
      {
        text: '我们会努力提高成绩的！',
        response: '好吧，我给你们一个机会。不过如果再输10场，我就去赞助恐龙园的恐龙了。',
        effect: '赞助+100万，压力+2'
      },
      {
        text: '我们虽然输球，但很搞笑啊！',
        response: '哈哈，这倒是真的！观众都是来看喜剧表演的。行，我赞助你们，当娱乐投资了。',
        effect: '赞助+80万，娱乐价值+3'
      }
    ]
  }
];

interface DialogSystemProps {
  isOpen: boolean;
  onClose: () => void;
  onEffect?: (effect: string) => void;
}

const DialogSystem: React.FC<DialogSystemProps> = ({ isOpen, onClose, onEffect }) => {
  const [currentDialog, setCurrentDialog] = useState<Dialog | null>(null);
  const [showResponse, setShowResponse] = useState(false);
  const [currentResponse, setCurrentResponse] = useState('');
  const [currentEffect, setCurrentEffect] = useState('');

  useEffect(() => {
    if (isOpen && !currentDialog) {
      // 随机选择一个对话
      const randomDialog = dialogDatabase[Math.floor(Math.random() * dialogDatabase.length)];
      setCurrentDialog(randomDialog);
      setShowResponse(false);
    }
  }, [isOpen, currentDialog]);

  const handleOptionClick = (option: any) => {
    setCurrentResponse(option.response);
    setCurrentEffect(option.effect || '');
    setShowResponse(true);
    
    if (option.effect && onEffect) {
      onEffect(option.effect);
    }
  };

  const handleClose = () => {
    setCurrentDialog(null);
    setShowResponse(false);
    setCurrentResponse('');
    setCurrentEffect('');
    onClose();
  };

  if (!isOpen || !currentDialog) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '16px',
        padding: '2rem',
        maxWidth: '500px',
        width: '90%',
        boxShadow: '0 20px 25px rgba(0,0,0,0.1)',
        border: '3px solid #fbbf24'
      }}>
        {/* 角色信息 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '1.5rem',
          padding: '1rem',
          background: '#fef3c7',
          borderRadius: '12px'
        }}>
          <div style={{
            fontSize: '3rem',
            marginRight: '1rem'
          }}>
            {currentDialog.avatar}
          </div>
          <div>
            <h3 style={{
              fontSize: '1.25rem',
              fontWeight: 'bold',
              color: '#92400e',
              margin: 0
            }}>
              {currentDialog.character}
            </h3>
            <p style={{
              fontSize: '0.875rem',
              color: '#d97706',
              margin: 0,
              fontStyle: 'italic'
            }}>
              正在和你对话...
            </p>
          </div>
        </div>

        {/* 对话内容 */}
        <div style={{
          background: '#f9fafb',
          borderRadius: '12px',
          padding: '1.5rem',
          marginBottom: '1.5rem',
          border: '2px solid #e5e7eb'
        }}>
          <p style={{
            fontSize: '1rem',
            lineHeight: '1.6',
            color: '#374151',
            margin: 0
          }}>
            {showResponse ? currentResponse : currentDialog.text}
          </p>
          
          {currentEffect && showResponse && (
            <div style={{
              marginTop: '1rem',
              padding: '0.5rem',
              background: '#dcfce7',
              borderRadius: '6px',
              border: '1px solid #16a34a'
            }}>
              <p style={{
                fontSize: '0.875rem',
                color: '#15803d',
                margin: 0,
                fontWeight: '500'
              }}>
                💫 效果：{currentEffect}
              </p>
            </div>
          )}
        </div>

        {/* 选项或关闭按钮 */}
        {!showResponse && currentDialog.options ? (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            {currentDialog.options.map((option, index) => (
              <button
                key={index}
                onClick={() => handleOptionClick(option)}
                style={{
                  padding: '0.75rem 1rem',
                  background: '#dbeafe',
                  border: '2px solid #3b82f6',
                  borderRadius: '8px',
                  color: '#1e40af',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'left'
                }}
                onMouseOver={(e) => {
                  e.target.style.background = '#bfdbfe';
                  e.target.style.transform = 'translateY(-1px)';
                }}
                onMouseOut={(e) => {
                  e.target.style.background = '#dbeafe';
                  e.target.style.transform = 'translateY(0)';
                }}
              >
                💬 {option.text}
              </button>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center' }}>
            <button
              onClick={handleClose}
              style={{
                padding: '0.75rem 2rem',
                background: '#fbbf24',
                border: 'none',
                borderRadius: '8px',
                color: 'white',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => {
                e.target.style.background = '#f59e0b';
                e.target.style.transform = 'scale(1.05)';
              }}
              onMouseOut={(e) => {
                e.target.style.background = '#fbbf24';
                e.target.style.transform = 'scale(1)';
              }}
            >
              ✨ 继续游戏
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DialogSystem;
